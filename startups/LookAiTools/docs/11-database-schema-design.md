# 数据库表结构设计 - 多语言架构

## 概述

基于数据收集需求分析，本文档提供了AI工具导航站的完整多语言数据库表结构设计，采用分离的翻译表架构，支持高性能查询和扩展。

## 设计特点

### 多语言支持
- 使用独立的翻译表存储多语言内容，支持 'en', 'cn', 'zh-CN' 等语言代码
- 分离的翻译表架构提供更好的查询性能和数据一致性
- 支持分类、工具、标签的完整多语言支持

### 核心架构
- `categories`: 分类主表，使用 `category_key` 作为唯一标识
- `category_translations`: 分类翻译表，存储多语言分类信息
- `tools`: 工具主表，存储基础信息和关联
- `tool_translations`: 工具翻译表，存储多语言工具信息
- `tags`: 标签主表，使用 `tag_key` 作为唯一标识
- `tag_translations`: 标签翻译表，存储多语言标签信息

### 性能优化
- 关系型表结构替代JSONB，提升查询性能
- 专门的索引策略支持多语言搜索
- 视图简化复杂查询逻辑

## 核心表结构

### 1. categories - 分类表

```sql
CREATE TABLE categories (
    id BIGSERIAL PRIMARY KEY,
    category_key VARCHAR(50) UNIQUE NOT NULL, -- 用于关联的唯一标识，如 'productivity'
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 2. category_translations - 分类多语言表

```sql
CREATE TABLE category_translations (
    id BIGSERIAL PRIMARY KEY,
    category_id BIGINT NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL,
    category_name VARCHAR(100) NOT NULL,
    category_description TEXT,

    UNIQUE(category_id, language_code)
);
```

### 3. tools - 主工具表

```sql
CREATE TABLE tools (
    id BIGSERIAL PRIMARY KEY,
    slug VARCHAR(100) UNIQUE NOT NULL,
    url TEXT NOT NULL,
    page_screenshot TEXT,
    category_id BIGINT NOT NULL REFERENCES categories(id),
    pricing_type VARCHAR(20) NOT NULL CHECK (pricing_type IN ('free', 'paid', 'freemium')),
    trial_available BOOLEAN DEFAULT FALSE,
    rating DECIMAL(3,2) DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
    view_count BIGINT DEFAULT 0,
    traffic_estimate BIGINT DEFAULT 0,
    featured BOOLEAN DEFAULT FALSE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT unique_slug UNIQUE (slug)
);
```

### 4. tool_translations - 工具多语言信息表

```sql
CREATE TABLE tool_translations (
    id BIGSERIAL PRIMARY KEY,
    tool_id BIGINT NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL, -- 'en', 'cn', 'zh-CN' 等
    name VARCHAR(200) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    long_description TEXT,
    use_cases TEXT,
    target_audience TEXT,
    subcategory VARCHAR(100),

    UNIQUE(tool_id, language_code)
);
```

### 5. tool_features - 功能特性表

```sql
CREATE TABLE tool_features (
    id BIGSERIAL PRIMARY KEY,
    tool_id BIGINT NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL,
    feature_text TEXT NOT NULL,
    sort_order INTEGER DEFAULT 0
);
```

### 6. tags - 标签表

```sql
CREATE TABLE tags (
    id BIGSERIAL PRIMARY KEY,
    tag_key VARCHAR(100) UNIQUE NOT NULL, -- 用于关联的唯一标识
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 7. tag_translations - 标签多语言表

```sql
CREATE TABLE tag_translations (
    id BIGSERIAL PRIMARY KEY,
    tag_id BIGINT NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL,
    tag_name VARCHAR(100) NOT NULL,

    UNIQUE(tag_id, language_code)
);
```

### 8. tool_tags - 工具标签关联表

```sql
CREATE TABLE tool_tags (
    tool_id BIGINT NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    tag_id BIGINT NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    tag_type VARCHAR(20) NOT NULL CHECK (tag_type IN ('general', 'industry')),

    PRIMARY KEY (tool_id, tag_id)
);
```

### 9. pricing_plans - 定价计划表

```sql
CREATE TABLE pricing_plans (
    id BIGSERIAL PRIMARY KEY,
    tool_id BIGINT NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL,
    plan_name VARCHAR(100) NOT NULL,
    price VARCHAR(50), -- 存储如 "$49.99", "免费" 等
    is_free_plan BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0
);
```

### 10. tool_metadata - 元数据表

```sql
CREATE TABLE tool_metadata (
    tool_id BIGINT PRIMARY KEY REFERENCES tools(id) ON DELETE CASCADE,
    source_site VARCHAR(100),
    crawl_timestamp TIMESTAMP WITH TIME ZONE,
    processing_timestamp TIMESTAMP WITH TIME ZONE,
    model_input_data TEXT -- JSON格式存储原始输入数据
);
```

## 索引优化

### 主要查询索引

```sql
-- 工具表索引
CREATE INDEX idx_tools_category ON tools(category_id);
CREATE INDEX idx_tools_pricing_type ON tools(pricing_type);
CREATE INDEX idx_tools_featured ON tools(featured);
CREATE INDEX idx_tools_status ON tools(status);
CREATE INDEX idx_tools_rating ON tools(rating DESC);
CREATE INDEX idx_tools_view_count ON tools(view_count DESC);

-- 多语言查询索引
CREATE INDEX idx_tool_translations_lang ON tool_translations(language_code);
CREATE INDEX idx_tool_translations_tool_lang ON tool_translations(tool_id, language_code);

-- 分类查询索引
CREATE INDEX idx_category_translations_lang ON category_translations(language_code);
CREATE INDEX idx_category_translations_cat_lang ON category_translations(category_id, language_code);

-- 全文搜索索引（支持中英文搜索）
CREATE INDEX idx_tool_translations_name_gin ON tool_translations USING gin(to_tsvector('english', name));
CREATE INDEX idx_tool_translations_title_gin ON tool_translations USING gin(to_tsvector('english', title));
CREATE INDEX idx_tool_translations_desc_gin ON tool_translations USING gin(to_tsvector('english', description));

-- 标签查询索引
CREATE INDEX idx_tool_tags_tool ON tool_tags(tool_id);
CREATE INDEX idx_tool_tags_tag ON tool_tags(tag_id);
CREATE INDEX idx_tool_tags_type ON tool_tags(tag_type);
```

## 常用查询视图

### 1. 工具详情视图（包含多语言信息和分类）

```sql
CREATE VIEW v_tool_details AS
SELECT
    t.id,
    t.slug,
    t.url,
    t.page_screenshot,
    c.category_key,
    ct.category_name,
    ct.category_description,
    t.pricing_type,
    t.trial_available,
    t.rating,
    t.view_count,
    t.traffic_estimate,
    t.featured,
    t.status,
    t.created_at,
    t.updated_at,
    tt.language_code,
    tt.name,
    tt.title,
    tt.description,
    tt.long_description,
    tt.use_cases,
    tt.target_audience,
    tt.subcategory
FROM tools t
LEFT JOIN categories c ON t.category_id = c.id
LEFT JOIN category_translations ct ON c.id = ct.category_id
LEFT JOIN tool_translations tt ON t.id = tt.tool_id AND tt.language_code = ct.language_code
WHERE t.status = 'active';
```

### 2. 工具搜索视图

```sql
CREATE VIEW v_tool_search AS
SELECT
    t.id,
    t.slug,
    c.category_key,
    ct.category_name,
    t.pricing_type,
    t.rating,
    t.featured,
    tt.language_code,
    tt.name,
    tt.title,
    tt.description,
    -- 创建搜索向量（包含分类名称）
    to_tsvector('english', COALESCE(tt.name, '') || ' ' || COALESCE(tt.title, '') || ' ' || COALESCE(tt.description, '') || ' ' || COALESCE(ct.category_name, '')) as search_vector
FROM tools t
LEFT JOIN categories c ON t.category_id = c.id
LEFT JOIN category_translations ct ON c.id = ct.category_id
LEFT JOIN tool_translations tt ON t.id = tt.tool_id AND tt.language_code = ct.language_code
WHERE t.status = 'active';
```

### 3. 分类列表视图

```sql
CREATE VIEW v_categories AS
SELECT
    c.id,
    c.category_key,
    c.sort_order,
    ct.language_code,
    ct.category_name,
    ct.category_description,
    COUNT(t.id) as tool_count
FROM categories c
LEFT JOIN category_translations ct ON c.id = ct.category_id
LEFT JOIN tools t ON c.id = t.category_id AND t.status = 'active'
GROUP BY c.id, c.category_key, c.sort_order, ct.language_code, ct.category_name, ct.category_description
ORDER BY c.sort_order;
```

## 常用查询示例

### 1. 按分类查询工具（支持多语言）

```sql
SELECT * FROM v_tool_details
WHERE category_key = 'productivity' AND language_code = 'en'
ORDER BY featured DESC, rating DESC, view_count DESC;
```

### 2. 获取所有分类（带工具数量统计）

```sql
SELECT * FROM v_categories
WHERE language_code = 'en'
ORDER BY sort_order;
```

### 3. 全文搜索（包含分类搜索）

```sql
SELECT DISTINCT id, slug, name, title, description, category_name
FROM v_tool_search
WHERE language_code = 'en'
AND search_vector @@ plainto_tsquery('english', 'AI assistant productivity')
ORDER BY ts_rank(search_vector, plainto_tsquery('english', 'AI assistant productivity')) DESC;
```

### 4. 获取热门工具

```sql
SELECT * FROM v_tool_details
WHERE language_code = 'en'
ORDER BY view_count DESC, rating DESC
LIMIT 20;
```

### 5. 按标签筛选

```sql
SELECT DISTINCT vt.*
FROM v_tool_details vt
JOIN tool_tags tts ON vt.id = tts.tool_id
JOIN tag_translations tt ON tts.tag_id = tt.tag_id
WHERE tt.language_code = 'en'
AND tt.tag_name = 'AI Assistant'
ORDER BY vt.rating DESC;
```

### 6. 组合筛选（分类 + 定价类型 + 评分）

```sql
SELECT * FROM v_tool_details
WHERE language_code = 'en'
AND category_key = 'productivity'
AND pricing_type IN ('free', 'freemium')
AND rating >= 4.0
ORDER BY featured DESC, rating DESC;
```

## 数据迁移和初始化

### 1. 使用新的数据库架构

```bash
# 创建新的数据库架构
cd backend
psql -d your_database -f create_new_schema.sql
```

### 2. 使用异步插入器导入数据

```bash
# 插入工具数据
cd crawler
python database_inserter_async.py tools_data.json --create-schema
```

### 3. 示例数据格式

```json
{
  "name": {"en": "Tool Name", "cn": "工具名称"},
  "title": {"en": "Display Title", "cn": "显示标题"},
  "slug": "tool-slug",
  "url": "https://example.com",
  "description": {"en": "Description", "cn": "描述"},
  "long_description": {"en": "Long description", "cn": "详细描述"},
  "category": "productivity",
  "subcategory": {"en": "Automation", "cn": "自动化"},
  "tags": [
    {"en": "AI", "cn": "人工智能"},
    {"en": "Productivity", "cn": "效率"}
  ],
  "key_features": [
    {"en": "Feature 1", "cn": "功能1"},
    {"en": "Feature 2", "cn": "功能2"}
  ],
  "use_cases": {"en": "Use cases", "cn": "使用场景"},
  "target_audience": {"en": "Target users", "cn": "目标用户"},
  "pricing_type": {"en": "freemium", "cn": "免费增值"},
  "trial_available": {"en": "yes", "cn": "是"},
  "rating": 4.5,
  "featured": true,
  "page_screenshot": "screenshots/tool.jpg"
}
```

## 性能优化建议

### 1. 查询优化

- 使用视图简化复杂的多表JOIN查询
- 利用索引优化常用查询路径
- 对于大量数据的搜索，考虑使用全文搜索索引

### 2. 维护建议

```sql
-- 定期更新表统计信息
ANALYZE tools;
ANALYZE tool_translations;
ANALYZE categories;

-- 监控索引使用情况
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

### 3. 扩展性考虑

- 支持更多语言：只需在翻译表中添加新的 language_code
- 新增分类：通过 categories 和 category_translations 表轻松扩展
- 自定义字段：可以在工具表中添加新字段而不影响现有结构

---

*文档更新时间: 2025-01-26*
*版本: v3.0*
*状态: 多语言数据库架构设计完成*
*更新内容: 采用分离翻译表架构，支持高性能多语言查询*
