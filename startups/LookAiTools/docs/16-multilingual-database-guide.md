# 多语言数据库架构使用指南

## 概述

本文档介绍如何使用新的多语言数据库架构，包括数据库创建、数据插入和查询操作。

## 快速开始

### 1. 创建数据库架构

```bash
# 进入后端目录
cd backend

# 创建数据库架构
psql -d your_database -f create_new_schema.sql

# 插入示例数据
psql -d your_database -f init_sample_data.sql
```

### 2. 使用异步插入器

```bash
# 进入爬虫目录
cd crawler

# 插入工具数据（首次使用时创建架构）
python database_inserter_async.py tools_data.json --create-schema

# 后续插入数据
python database_inserter_async.py tools_data.json
```

## 数据格式要求

### 工具数据JSON格式

```json
{
  "name": {"en": "Tool Name", "cn": "工具名称"},
  "title": {"en": "Display Title", "cn": "显示标题"},
  "slug": "tool-slug",
  "url": "https://example.com",
  "description": {"en": "Description", "cn": "描述"},
  "long_description": {"en": "Long description", "cn": "详细描述"},
  "category": "productivity",
  "subcategory": {"en": "Automation", "cn": "自动化"},
  "tags": [
    {"en": "AI", "cn": "人工智能"},
    {"en": "Productivity", "cn": "效率"}
  ],
  "industry_tags": [
    {"en": "Business", "cn": "商业"},
    {"en": "Technology", "cn": "技术"}
  ],
  "key_features": [
    {"en": "Feature 1", "cn": "功能1"},
    {"en": "Feature 2", "cn": "功能2"}
  ],
  "use_cases": {"en": "Use cases", "cn": "使用场景"},
  "target_audience": {"en": "Target users", "cn": "目标用户"},
  "pricing_type": {"en": "freemium", "cn": "免费增值"},
  "trial_available": {"en": "yes", "cn": "是"},
  "rating": 4.5,
  "view_count": 1000,
  "traffic_estimate": 50000,
  "featured": true,
  "page_screenshot": "screenshots/tool.jpg"
}
```

## 常用查询

### 1. 获取工具列表（英文）

```sql
SELECT 
    t.id, t.slug, tt.name, tt.title, tt.description,
    ct.category_name, t.rating, t.featured
FROM tools t
JOIN tool_translations tt ON t.id = tt.tool_id AND tt.language_code = 'en'
JOIN categories c ON t.category_id = c.id
JOIN category_translations ct ON c.id = ct.category_id AND ct.language_code = 'en'
WHERE t.status = 'active'
ORDER BY t.featured DESC, t.rating DESC;
```

### 2. 按分类查询工具（中文）

```sql
SELECT * FROM v_tool_details 
WHERE category_key = 'productivity' AND language_code = 'cn'
ORDER BY featured DESC, rating DESC;
```

### 3. 全文搜索

```sql
SELECT DISTINCT id, slug, name, title, description
FROM v_tool_search 
WHERE language_code = 'en' 
AND search_vector @@ plainto_tsquery('english', 'AI assistant')
ORDER BY ts_rank(search_vector, plainto_tsquery('english', 'AI assistant')) DESC;
```

### 4. 获取分类列表

```sql
SELECT * FROM v_categories 
WHERE language_code = 'en'
ORDER BY sort_order;
```

## 数据维护

### 1. 添加新分类

```sql
-- 插入分类主数据
INSERT INTO categories (category_key, sort_order) 
VALUES ('new-category', 99);

-- 插入分类翻译
INSERT INTO category_translations (category_id, language_code, category_name, category_description)
VALUES 
((SELECT id FROM categories WHERE category_key = 'new-category'), 'en', 'New Category', 'Description'),
((SELECT id FROM categories WHERE category_key = 'new-category'), 'cn', '新分类', '描述');
```

### 2. 添加新标签

```sql
-- 插入标签主数据
INSERT INTO tags (tag_key) VALUES ('new-tag');

-- 插入标签翻译
INSERT INTO tag_translations (tag_id, language_code, tag_name)
VALUES 
((SELECT id FROM tags WHERE tag_key = 'new-tag'), 'en', 'New Tag'),
((SELECT id FROM tags WHERE tag_key = 'new-tag'), 'cn', '新标签');
```

### 3. 更新工具信息

```sql
-- 更新工具基础信息
UPDATE tools SET rating = 4.8 WHERE slug = 'tool-slug';

-- 更新工具翻译信息
UPDATE tool_translations 
SET description = 'Updated description'
WHERE tool_id = (SELECT id FROM tools WHERE slug = 'tool-slug')
AND language_code = 'en';
```

## 性能优化

### 1. 监控查询性能

```sql
-- 查看慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
WHERE mean_time > 100 
ORDER BY mean_time DESC;

-- 查看索引使用情况
SELECT schemaname, tablename, indexname, idx_scan
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

### 2. 维护建议

```sql
-- 定期更新统计信息
ANALYZE tools;
ANALYZE tool_translations;
ANALYZE categories;
ANALYZE category_translations;

-- 重建索引（如果需要）
REINDEX TABLE tool_translations;
```

## 故障排除

### 1. 常见错误

**错误**: 分类不存在
```
解决方案: 检查 categories 表中是否存在对应的 category_key
```

**错误**: 翻译数据缺失
```
解决方案: 确保每个工具都有对应的英文和中文翻译记录
```

**错误**: 外键约束违反
```
解决方案: 确保引用的分类ID和标签ID存在于对应的主表中
```

### 2. 数据一致性检查

```sql
-- 检查缺失翻译的工具
SELECT t.id, t.slug 
FROM tools t
LEFT JOIN tool_translations tt ON t.id = tt.tool_id AND tt.language_code = 'en'
WHERE tt.id IS NULL;

-- 检查缺失翻译的分类
SELECT c.id, c.category_key 
FROM categories c
LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.language_code = 'en'
WHERE ct.id IS NULL;
```

## 备份和恢复

### 1. 备份数据库

```bash
# 完整备份
pg_dump -h localhost -U username -d database_name > backup.sql

# 仅备份数据
pg_dump -h localhost -U username -d database_name --data-only > data_backup.sql
```

### 2. 恢复数据库

```bash
# 恢复完整备份
psql -h localhost -U username -d database_name < backup.sql

# 仅恢复数据
psql -h localhost -U username -d database_name < data_backup.sql
```

## 扩展支持

### 1. 添加新语言

```sql
-- 为所有分类添加新语言翻译
INSERT INTO category_translations (category_id, language_code, category_name, category_description)
SELECT id, 'fr', 'French Name', 'French Description'
FROM categories;

-- 为所有工具添加新语言翻译
INSERT INTO tool_translations (tool_id, language_code, name, title, description)
SELECT id, 'fr', 'French Name', 'French Title', 'French Description'
FROM tools;
```

### 2. 自定义字段

如需添加新字段，可以直接在相应表中添加列：

```sql
-- 在工具表中添加新字段
ALTER TABLE tools ADD COLUMN custom_field VARCHAR(255);

-- 在翻译表中添加多语言字段
ALTER TABLE tool_translations ADD COLUMN custom_description TEXT;
```

---

*文档创建时间: 2025-01-26*
*版本: v1.0*
*状态: 多语言数据库使用指南*
