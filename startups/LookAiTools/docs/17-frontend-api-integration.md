# 前端API集成指南 - 多语言架构

## 概述

本文档介绍如何在前端应用中集成新的多语言API，实现中英文切换功能。

## API服务器信息

- **新多语言API**: `http://localhost:8001`
- **原API**: `http://localhost:8000`
- **架构版本**: v2.0.0 (多语言架构)

## 主要API端点

### 1. 健康检查

```bash
GET /health
```

**响应示例**:
```json
{
  "status": "healthy",
  "database": "connected",
  "architecture": "multilingual"
}
```

### 2. 获取分类列表

```bash
GET /api/categories?language={en|cn}
```

**参数**:
- `language`: 语言代码 (`en` 或 `cn`)

**响应示例**:
```json
{
  "data": [
    {
      "id": "productivity",
      "name": "Productivity",  // 根据language参数返回对应语言
      "slug": "productivity",
      "description": "Tools to enhance your productivity and efficiency",
      "count": 40
    }
  ],
  "success": true
}
```

### 3. 获取工具列表

```bash
GET /api/tools?page=1&limit=12&language={en|cn}&category={category}&search={keyword}
```

**参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 12, 最大: 100)
- `language`: 语言代码 (`en` 或 `cn`)
- `category`: 分类筛选 (可选)
- `tags`: 标签筛选 (可选)
- `featured`: 是否精选 (可选)
- `search`: 搜索关键词 (可选)

**响应示例**:
```json
{
  "data": [
    {
      "id": 45,
      "slug": "ai-watermark-remover",
      "name": "AI WATERMARK REMOVER",  // 根据language参数返回对应语言
      "title": "AI Watermark Remover - Remove Watermarks from Images Online",
      "description": "Advanced AI-powered watermark removal tool...",
      "url": "https://www.aiwatermarkremover.com",
      "thumbnail_url": "https://lookforai.s3.us-east-1.amazonaws.com/aitools/Ai_Watermark_Remover.jpg",
      "category": "productivity",
      "category_name": "Productivity",  // 根据language参数返回对应语言
      "pricing_type": "freemium",
      "rating": 4.9,
      "view_count": 0,
      "featured": false,
      "tags": ["watermark removal", "image editing"],  // 根据language参数返回对应语言
      "created_at": "2025-07-26 08:10:46.731055+00:00",
      "trial_available": true,
      "traffic_estimate": 0
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 12,
    "total": 45,
    "total_pages": 4
  },
  "success": true
}
```

### 4. 获取工具详情

```bash
GET /api/tools/{slug_or_id}?language={en|cn}
```

**参数**:
- `slug_or_id`: 工具的slug或ID
- `language`: 语言代码 (`en` 或 `cn`)

**响应示例**:
```json
{
  "data": {
    "id": 45,
    "slug": "ai-watermark-remover",
    "name": "AI WATERMARK REMOVER",
    "title": "AI Watermark Remover - Remove Watermarks from Images Online",
    "description": "Advanced AI-powered watermark removal tool...",
    "long_description": "This is an advanced AI watermark removal tool...",
    "url": "https://www.aiwatermarkremover.com",
    "thumbnail_url": "https://lookforai.s3.us-east-1.amazonaws.com/aitools/Ai_Watermark_Remover.jpg",
    "category": "productivity",
    "category_name": "Productivity",
    "category_description": "Tools to enhance your productivity and efficiency",
    "pricing_type": "freemium",
    "rating": 4.9,
    "view_count": 0,
    "featured": false,
    "tags": ["watermark removal", "image editing", "AI processing"],
    "industry_tags": ["E-commerce", "design", "Marketing"],
    "key_features": [
      "Smart AI detection of watermarks",
      "Perfect preservation of image quality",
      "Lightning fast 2-5 seconds processing"
    ],
    "use_cases": "Personal photo watermark removal, e-commerce product image processing...",
    "target_audience": "Individual users, e-commerce sellers, designers, marketers",
    "subcategory": "Image Restoration and Editing",
    "trial_available": true,
    "traffic_estimate": 0,
    "created_at": "2025-07-26 08:10:46.731055+00:00"
  },
  "success": true
}
```

## 前端集成示例

### React/Vue.js 集成

```javascript
// API配置
const API_BASE_URL = 'http://localhost:8001';

// API客户端类
class LookAIToolsAPI {
  constructor(language = 'en') {
    this.language = language;
    this.baseURL = API_BASE_URL;
  }

  // 设置语言
  setLanguage(language) {
    this.language = language;
  }

  // 获取分类列表
  async getCategories() {
    const response = await fetch(`${this.baseURL}/api/categories?language=${this.language}`);
    return response.json();
  }

  // 获取工具列表
  async getTools(params = {}) {
    const queryParams = new URLSearchParams({
      language: this.language,
      page: params.page || 1,
      limit: params.limit || 12,
      ...params
    });
    
    const response = await fetch(`${this.baseURL}/api/tools?${queryParams}`);
    return response.json();
  }

  // 获取工具详情
  async getTool(slugOrId) {
    const response = await fetch(`${this.baseURL}/api/tools/${slugOrId}?language=${this.language}`);
    return response.json();
  }

  // 搜索工具
  async searchTools(keyword, params = {}) {
    return this.getTools({
      search: keyword,
      ...params
    });
  }

  // 按分类获取工具
  async getToolsByCategory(category, params = {}) {
    return this.getTools({
      category: category,
      ...params
    });
  }
}

// 使用示例
const api = new LookAIToolsAPI('en');

// 切换到中文
api.setLanguage('cn');

// 获取分类列表
const categories = await api.getCategories();

// 获取工具列表
const tools = await api.getTools({ page: 1, limit: 12 });

// 搜索工具
const searchResults = await api.searchTools('AI watermark');

// 按分类获取工具
const productivityTools = await api.getToolsByCategory('productivity');

// 获取工具详情
const toolDetail = await api.getTool('ai-watermark-remover');
```

### 语言切换组件示例

```javascript
// React语言切换组件
import React, { useState, useContext } from 'react';

const LanguageContext = React.createContext();

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('en');
  
  return (
    <LanguageContext.Provider value={{ language, setLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const LanguageSwitcher = () => {
  const { language, setLanguage } = useContext(LanguageContext);
  
  return (
    <div className="language-switcher">
      <button 
        className={language === 'en' ? 'active' : ''}
        onClick={() => setLanguage('en')}
      >
        English
      </button>
      <button 
        className={language === 'cn' ? 'active' : ''}
        onClick={() => setLanguage('cn')}
      >
        中文
      </button>
    </div>
  );
};

// 使用语言上下文的组件
export const ToolsList = () => {
  const { language } = useContext(LanguageContext);
  const [tools, setTools] = useState([]);
  
  useEffect(() => {
    const api = new LookAIToolsAPI(language);
    api.getTools().then(response => {
      setTools(response.data);
    });
  }, [language]);
  
  return (
    <div className="tools-list">
      {tools.map(tool => (
        <div key={tool.id} className="tool-card">
          <h3>{tool.name}</h3>
          <p>{tool.description}</p>
          <span className="category">{tool.category_name}</span>
        </div>
      ))}
    </div>
  );
};
```

## 迁移指南

### 从旧API迁移到新API

1. **更新API基础URL**:
   ```javascript
   // 旧版本
   const API_BASE_URL = 'http://localhost:8000';
   
   // 新版本
   const API_BASE_URL = 'http://localhost:8001';
   ```

2. **添加语言参数**:
   ```javascript
   // 旧版本
   fetch('/api/tools')
   
   // 新版本
   fetch('/api/tools?language=en')
   ```

3. **更新响应数据结构**:
   - 工具名称、标题、描述现在直接返回对应语言的文本
   - 分类名称根据语言参数返回对应翻译
   - 标签数组现在是字符串数组而不是对象数组

4. **处理多语言内容**:
   ```javascript
   // 旧版本 - 手动处理多语言对象
   const toolName = tool.name[language] || tool.name.en;
   
   // 新版本 - 直接使用返回的文本
   const toolName = tool.name;
   ```

## 测试命令

```bash
# 测试健康检查
curl "http://localhost:8001/health"

# 测试英文分类
curl "http://localhost:8001/api/categories?language=en"

# 测试中文分类
curl "http://localhost:8001/api/categories?language=cn"

# 测试英文工具列表
curl "http://localhost:8001/api/tools?language=en&page=1&limit=3"

# 测试中文工具列表
curl "http://localhost:8001/api/tools?language=cn&page=1&limit=3"

# 测试工具详情
curl "http://localhost:8001/api/tools/ai-watermark-remover?language=en"
curl "http://localhost:8001/api/tools/ai-watermark-remover?language=cn"

# 测试搜索
curl "http://localhost:8001/api/tools?search=AI&language=en"

# 测试分类筛选
curl "http://localhost:8001/api/tools?category=productivity&language=cn"
```

## 注意事项

1. **语言参数**: 所有API调用都应包含 `language` 参数
2. **向后兼容**: 如果不提供语言参数，默认返回英文内容
3. **错误处理**: API返回的错误信息会根据语言参数本地化
4. **性能**: 新架构使用关系型表结构，查询性能更好
5. **缓存**: 建议在前端实现适当的缓存机制以提升用户体验

---

*文档创建时间: 2025-07-26*
*API版本: v2.0.0*
*状态: 多语言API集成指南*
