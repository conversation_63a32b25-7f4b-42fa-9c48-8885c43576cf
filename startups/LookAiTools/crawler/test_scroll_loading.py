#!/usr/bin/env python3
"""
测试滚动加载机制
"""
import asyncio
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup

async def test_scroll_loading():
    """测试滚动加载"""
    target_url = "https://www.toolify.ai/new"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # 可视化模式
        page = await browser.new_page()
        
        try:
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })

            print(f"📱 访问页面: {target_url}")
            await page.goto(target_url, wait_until='domcontentloaded', timeout=60000)
            await page.wait_for_timeout(5000)
            
            # 初始工具数量
            content = await page.content()
            soup = BeautifulSoup(content, 'html.parser')
            initial_tools = soup.select('.grid a[href*="/tool/"]')
            print(f"📊 初始工具数量: {len(initial_tools)}")
            
            # 记录所有工具链接
            all_hrefs = set()
            for tool in initial_tools:
                href = tool.get('href', '')
                if href:
                    all_hrefs.add(href)
            
            print(f"📊 初始唯一工具链接: {len(all_hrefs)}")
            
            # 尝试不同的滚动策略
            scroll_strategies = [
                "window.scrollTo(0, document.body.scrollHeight)",
                "window.scrollBy(0, window.innerHeight)",
                "window.scrollBy(0, 1000)",
                "document.documentElement.scrollTop = document.documentElement.scrollHeight"
            ]
            
            for i, strategy in enumerate(scroll_strategies, 1):
                print(f"\n🔄 测试滚动策略 {i}: {strategy}")
                
                # 执行滚动
                await page.evaluate(strategy)
                await page.wait_for_timeout(5000)
                
                # 等待网络请求
                try:
                    await page.wait_for_load_state('networkidle', timeout=15000)
                except:
                    print("   ⏳ 网络空闲等待超时")
                
                # 检查新工具
                content = await page.content()
                soup = BeautifulSoup(content, 'html.parser')
                current_tools = soup.select('.grid a[href*="/tool/"]')
                
                current_hrefs = set()
                for tool in current_tools:
                    href = tool.get('href', '')
                    if href:
                        current_hrefs.add(href)
                
                new_hrefs = current_hrefs - all_hrefs
                print(f"   📊 当前总工具: {len(current_tools)}")
                print(f"   📊 当前唯一链接: {len(current_hrefs)}")
                print(f"   📊 新增链接: {len(new_hrefs)}")
                
                if new_hrefs:
                    print(f"   ✅ 发现新工具！")
                    all_hrefs.update(new_hrefs)
                    # 显示几个新工具的例子
                    for j, href in enumerate(list(new_hrefs)[:3]):
                        print(f"      新工具 {j+1}: {href}")
                else:
                    print(f"   ❌ 未发现新工具")
                
                # 检查页面是否有加载指示器
                loading_indicators = soup.select('[class*="loading"], [class*="spinner"], [class*="load"]')
                if loading_indicators:
                    print(f"   🔄 发现加载指示器: {len(loading_indicators)} 个")
            
            # 尝试更激进的滚动
            print(f"\n🔄 尝试连续滚动...")
            for i in range(10):
                await page.evaluate("window.scrollBy(0, 500)")
                await page.wait_for_timeout(1000)
                
                if i % 3 == 0:  # 每3次检查一次
                    content = await page.content()
                    soup = BeautifulSoup(content, 'html.parser')
                    current_tools = soup.select('.grid a[href*="/tool/"]')
                    
                    current_hrefs = set()
                    for tool in current_tools:
                        href = tool.get('href', '')
                        if href:
                            current_hrefs.add(href)
                    
                    new_hrefs = current_hrefs - all_hrefs
                    if new_hrefs:
                        print(f"   ✅ 第{i+1}次滚动发现 {len(new_hrefs)} 个新工具")
                        all_hrefs.update(new_hrefs)
                    else:
                        print(f"   📊 第{i+1}次滚动: 总计 {len(current_hrefs)} 个工具")
            
            print(f"\n🎯 最终统计:")
            print(f"📊 总计发现 {len(all_hrefs)} 个唯一工具链接")
            
            # 等待观察
            print(f"\n⏳ 等待30秒以便观察...")
            await page.wait_for_timeout(30000)
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_scroll_loading())
