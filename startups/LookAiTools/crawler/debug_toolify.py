#!/usr/bin/env python3
"""
调试Toolify页面的详细脚本
"""
import asyncio
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup

async def debug_toolify():
    """详细调试Toolify页面"""
    target_url = "https://www.toolify.ai/new"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        try:
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })

            print(f"📱 访问页面: {target_url}")
            await page.goto(target_url, wait_until='domcontentloaded', timeout=60000)
            await page.wait_for_timeout(5000)
            
            # 检查初始状态
            print("\n🔍 检查初始页面状态...")
            content = await page.content()
            soup = BeautifulSoup(content, 'html.parser')
            
            # 测试所有可能的选择器
            selectors = [
                '.grid a[href*="/tool/"]',
                'a[href*="/tool/"]',
                '.tool-item a[href*="/tool/"]',
                '.grid a',
                'a[href^="/tool/"]'
            ]
            
            for selector in selectors:
                elements = soup.select(selector)
                print(f"   {selector}: {len(elements)} 个元素")
            
            # 记录初始工具
            initial_tools = soup.select('.grid a[href*="/tool/"]')
            initial_hrefs = set()
            for tool in initial_tools:
                href = tool.get('href', '')
                if href:
                    initial_hrefs.add(href)
            
            print(f"\n📊 初始状态: {len(initial_tools)} 个工具元素, {len(initial_hrefs)} 个唯一链接")
            
            # 检查页面是否有无限滚动的指示器
            print("\n🔍 检查页面结构...")
            
            # 查找可能的加载更多按钮
            load_more_buttons = soup.select('button:contains("Load"), button:contains("More"), [class*="load"], [class*="more"]')
            print(f"   加载更多按钮: {len(load_more_buttons)} 个")
            
            # 查找分页元素
            pagination = soup.select('[class*="pagination"], [class*="page"], .next, .prev')
            print(f"   分页元素: {len(pagination)} 个")
            
            # 查找工具容器
            containers = soup.select('.grid, [class*="grid"], [class*="container"], [class*="tools"]')
            print(f"   工具容器: {len(containers)} 个")
            for i, container in enumerate(containers[:3]):
                print(f"      容器 {i+1}: {container.name} - {container.get('class', [])} - 子元素: {len(container.find_all())}")
            
            # 尝试滚动并观察变化
            print(f"\n🔄 开始滚动测试...")
            
            for i in range(10):
                print(f"\n--- 第{i+1}次滚动 ---")
                
                # 滚动前的状态
                before_content = await page.content()
                before_soup = BeautifulSoup(before_content, 'html.parser')
                before_tools = before_soup.select('.grid a[href*="/tool/"]')
                before_hrefs = set()
                for tool in before_tools:
                    href = tool.get('href', '')
                    if href:
                        before_hrefs.add(href)
                
                print(f"   滚动前: {len(before_tools)} 个工具元素, {len(before_hrefs)} 个唯一链接")
                
                # 执行滚动
                if i % 3 == 0:
                    await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                    print("   滚动方式: scrollTo(0, document.body.scrollHeight)")
                elif i % 3 == 1:
                    await page.evaluate("window.scrollBy(0, 1000)")
                    print("   滚动方式: scrollBy(0, 1000)")
                else:
                    await page.evaluate("document.documentElement.scrollTop = document.documentElement.scrollHeight")
                    print("   滚动方式: documentElement.scrollTop")
                
                # 等待
                await page.wait_for_timeout(4000)
                
                # 尝试等待网络
                try:
                    await page.wait_for_load_state('networkidle', timeout=8000)
                    print("   ✅ 网络空闲")
                except:
                    print("   ⏳ 网络空闲超时")
                
                # 滚动后的状态
                after_content = await page.content()
                after_soup = BeautifulSoup(after_content, 'html.parser')
                after_tools = after_soup.select('.grid a[href*="/tool/"]')
                after_hrefs = set()
                for tool in after_tools:
                    href = tool.get('href', '')
                    if href:
                        after_hrefs.add(href)
                
                print(f"   滚动后: {len(after_tools)} 个工具元素, {len(after_hrefs)} 个唯一链接")
                
                # 检查变化
                new_hrefs = after_hrefs - before_hrefs
                if new_hrefs:
                    print(f"   ✅ 发现 {len(new_hrefs)} 个新工具!")
                    for j, href in enumerate(list(new_hrefs)[:3]):
                        print(f"      新工具 {j+1}: {href}")
                else:
                    print(f"   ❌ 未发现新工具")
                
                # 检查页面高度变化
                height = await page.evaluate("document.body.scrollHeight")
                scroll_top = await page.evaluate("window.pageYOffset")
                print(f"   页面高度: {height}, 滚动位置: {scroll_top}")
                
                # 如果连续3次没有新工具，尝试其他方法
                if i >= 3 and len(new_hrefs) == 0:
                    print(f"   🔄 尝试点击加载更多...")
                    
                    # 查找并点击加载更多按钮
                    try:
                        load_more = await page.query_selector('button:has-text("Load"), button:has-text("More"), [class*="load-more"]')
                        if load_more:
                            await load_more.click()
                            print("   ✅ 点击了加载更多按钮")
                            await page.wait_for_timeout(3000)
                        else:
                            print("   ❌ 未找到加载更多按钮")
                    except Exception as e:
                        print(f"   ❌ 点击失败: {e}")
            
            # 最终统计
            final_content = await page.content()
            final_soup = BeautifulSoup(final_content, 'html.parser')
            final_tools = final_soup.select('.grid a[href*="/tool/"]')
            final_hrefs = set()
            for tool in final_tools:
                href = tool.get('href', '')
                if href:
                    final_hrefs.add(href)
            
            print(f"\n🎯 最终结果:")
            print(f"   工具元素: {len(final_tools)} 个")
            print(f"   唯一链接: {len(final_hrefs)} 个")
            print(f"   相比初始增加: {len(final_hrefs) - len(initial_hrefs)} 个")
            
            # 等待观察
            print(f"\n⏳ 等待30秒以便观察...")
            await page.wait_for_timeout(30000)
            
        except Exception as e:
            print(f"❌ 调试失败: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_toolify())
