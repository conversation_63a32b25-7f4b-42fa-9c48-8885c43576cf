# AI工具数据标准化处理 (支持本地模型)

## 概述

本模块支持使用AWS Bedrock Claude或本地部署的AI模型将爬取的原始AI工具数据标准化为数据库格式，支持中英文双语内容生成。重构后的代码结构更清晰，功能模块化更好。

## 功能特性

- ✅ 支持AWS Bedrock Claude和本地AI模型进行智能数据标准化
- ✅ 自动生成中英文双语内容
- ✅ 智能分类映射和标签标准化
- ✅ 定价信息结构化提取
- ✅ 数据质量评分和验证
- ✅ 备用数据处理机制
- ✅ 批量处理和进度监控
- ✅ 模块化代码结构，易于维护和扩展
- ✅ 分离的系统提示词和用户提示词
- ✅ 结构化的输入数据格式化
- ✅ 灵活的模型选择: 支持AWS Bedrock和本地模型，可根据需求切换
- ✅ 增量保存和断点续传: 防止程序崩溃导致数据丢失，支持从中断处继续

## 环境配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置AI模型

#### 选项1: AWS Bedrock (默认)

确保 `.env` 文件中包含正确的AWS配置：

```env
# AWS Bedrock Claude配置
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key
AWS_REGION=us-west-2
BEDROCK_MODEL_ID=anthropic.claude-3-haiku-20240307-v1:0
```

#### 选项2: 本地模型

如果使用本地部署的模型，配置以下参数：

```env
# 本地模型配置
LOCAL_API_URL=http://localhost:8989/v1/chat/completions
LOCAL_API_KEY=ki2api-key-2024
LOCAL_MODEL_NAME=claude-sonnet-4-20250514
```

### 3. 验证配置

#### AWS Bedrock模式验证

```bash
python test_refactored.py
```

#### 本地模型验证

```bash
python test_local_model.py
```

#### 完整功能测试

```bash
python test_standardizer.py
```

## 使用方法

### 批量数据标准化

#### 使用AWS Bedrock (默认)

```bash
# 使用默认配置处理所有工具
python data_standardizer.py

# 指定输入输出文件
python data_standardizer.py --input custom_input.json --output custom_output_dir
```

#### 使用本地模型

```bash
# 使用本地模型处理所有工具
python data_standardizer.py --local

# 使用本地模型并指定文件
python data_standardizer.py --local --input custom_input.json --output custom_output_dir

# 设置保存间隔（每处理10个工具保存一次）
python data_standardizer.py --save-interval 10

# 从进度文件恢复处理
python data_standardizer.py --resume tools_info/toolify/progress_20250124_123456.json
```

### 测试模式

#### 重构验证测试（推荐先运行）

```bash
# 测试重构后的代码结构 (AWS Bedrock)
python test_refactored.py
```

#### 本地模型测试

```bash
# 测试本地模型连接和功能
python test_local_model.py
```

#### 功能测试模式

```bash
# 测试3个工具的标准化效果
python test_standardizer.py
```

功能测试会：
- 提取3个样本工具进行测试
- 显示详细的处理过程和结果
- 计算数据质量评分
- 保存测试结果到 `tools_info/toolify/test_results_*.json`

#### 增量保存测试

```bash
# 测试增量保存和断点续传功能
python test_incremental_save.py
```

增量保存测试会：
- 处理10个工具验证增量保存
- 测试断点续传功能
- 验证进度文件的正确性

### 批量处理模式

```bash
# 处理所有工具数据
python data_standardizer.py
```

批量处理会：
- 处理所有爬取的工具数据
- 自动控制API调用频率
- 生成处理报告
- 保存结果到 `tools_info/toolify/standardized_tools_*.json`

### 增量保存和断点续传

#### 功能特点

- **自动增量保存**: 每处理指定数量的工具自动保存结果
- **进度跟踪**: 记录已处理的工具URL，避免重复处理
- **断点续传**: 程序崩溃后可从中断处继续处理
- **数据安全**: 防止长时间处理后因意外丢失所有数据

#### 工作原理

1. **进度文件**: 自动生成 `progress_timestamp.json` 记录处理进度
2. **增量保存**: 默认每5个工具保存一次，可通过 `--save-interval` 调整
3. **自动恢复**: 重新运行时自动检测未完成任务并继续处理
4. **手动恢复**: 使用 `--resume` 参数指定进度文件恢复

#### 使用示例

```bash
# 开始处理（每3个工具保存一次）
python data_standardizer.py --save-interval 3

# 如果程序中断，重新运行会自动继续
python data_standardizer.py

# 或手动指定进度文件恢复
python data_standardizer.py --resume tools_info/toolify/progress_20250124_123456.json
```

## 输出数据格式

标准化后的数据包含以下字段：

```json
{
  "name": "chat-gpt",
  "title": "ChatGPT",
  "title_zh": "ChatGPT聊天机器人",
  "slug": "chat-gpt",
  "url": "https://chat.openai.com",
  "description": "AI-powered conversational assistant for various tasks",
  "description_zh": "基于AI的对话助手，可协助完成各种任务",
  "long_description": "Detailed description...",
  "long_description_zh": "详细描述...",
  "key_features": [
    {"en": "Natural language processing", "zh": "自然语言处理"},
    {"en": "Multi-turn conversations", "zh": "多轮对话"}
  ],
  "use_cases": "Customer support, content creation, coding assistance",
  "target_audience": "Developers, content creators, businesses",
  "category": "chatbot",
  "subcategory": "conversational-ai",
  "tags": ["chatbot", "ai-assistant", "nlp"],
  "industry_tags": ["technology", "customer-service"],
  "pricing_type": "freemium",
  "pricing_details": {
    "free_plan": "Limited usage with basic features",
    "paid_plans": [
      {
        "name": "Plus",
        "price": "$20/month",
        "features": ["Priority access", "Faster responses"]
      }
    ]
  },
  "trial_available": true,
  "rating": 0,
  "view_count": 0,
  "traffic_estimate": 0,
  "featured": false,
  "status": "active",
  "meta_info": {
    "source_site": "toolify",
    "crawl_timestamp": "2025-01-24 13:46:20",
    "processing_timestamp": "2025-01-24T15:30:00",
    "raw_data": {...}
  }
}
```

## 数据质量评分

系统会自动计算每个工具的数据质量评分（0-100分）：

- **必需字段完整性** (40分): name, title, description, category, url
- **中文内容完整性** (20分): title_zh, description_zh
- **详细信息完整性** (20分): long_description, key_features, pricing_details
- **标签和分类** (10分): 有效的标签和分类
- **内容质量** (10分): 描述长度合理性、特性数量等

## 分类映射

系统支持以下标准分类：

- `writing` - 写作工具
- `image-generation` - 图像生成
- `coding` - 编程助手
- `chatbot` - 聊天机器人
- `video` - 视频制作
- `audio` - 音频处理
- `data-analysis` - 数据分析
- `productivity` - 效率工具
- `design` - 设计工具
- `research` - 研究工具

## 错误处理

- **API调用失败**: 自动使用备用数据处理
- **JSON解析错误**: 回退到基础数据提取
- **网络超时**: 自动重试机制
- **数据验证失败**: 使用默认值和清理规则

## 性能优化

- **请求频率控制**: 每10个请求暂停2秒
- **批量处理**: 支持大规模数据处理
- **内存优化**: 流式处理避免内存溢出
- **进度监控**: 实时显示处理进度

## 故障排除

### 1. AWS连接失败

```
✗ AWS Bedrock 连接失败: NoCredentialsError
```

**解决方案**: 检查 `.env` 文件中的AWS凭证配置

### 2. JSON解析错误

```
JSON解析失败: Expecting value: line 1 column 1 (char 0)
```

**解决方案**: LLM返回格式异常，系统会自动使用备用数据

### 3. 分类映射失败

**解决方案**: 系统会自动映射到 `productivity` 默认分类

## 输出文件

- **测试结果**: `tools_info/toolify/test_results_YYYYMMDD_HHMMSS.json`
- **标准化数据**: `tools_info/toolify/standardized_tools_YYYYMMDD_HHMMSS.json`

## 代码结构说明

### 核心类: AWSBedrockStandardizer

重构后的代码采用模块化设计：

```python
class AWSBedrockStandardizer:
    def format_input_data(self, raw_data) -> str:
        """格式化输入数据为结构化文本"""
        # 将原始JSON数据转换为LLM友好的文本格式
        # 提取所有可用字段，包括基础信息、产品信息、网页信息等

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        # 定义AI助手的角色和任务

    def get_user_prompt(self, formatted_data) -> str:
        """获取用户提示词"""
        # 包含具体的分析任务和输出格式要求

    def call_bedrock_claude(self, system_prompt, user_prompt) -> str:
        """调用AWS Bedrock Claude API"""
        # 分离的系统和用户提示词调用

    def validate_and_clean_data(self, data, raw_data) -> dict:
        """验证和清理标准化后的数据"""
        # 数据验证和格式转换
```

### 主要改进

1. **分离关注点**: 输入格式化、提示词生成、API调用各自独立
2. **系统/用户提示词分离**: 更符合Claude API的最佳实践
3. **模块化设计**: 每个函数职责单一，易于测试和维护
4. **更好的错误处理**: 各个环节都有独立的错误处理机制
5. **完整数据提取**: 提取所有JSON字段，包括产品信息、网页内容、元数据等
6. **智能分类生成**: 完全依赖LLM基于工具实际功能进行分类，无需预设映射表
7. **更新的分类体系**: 根据前端UI更新了17个分类选项
8. **关键字段支持**: 包含前端必需的 `page_screenshot` 字段用于卡片显示

### 智能分类生成

移除了预设的分类映射表，完全依赖LLM基于工具的实际功能进行智能分类：

**支持的分类选项**（17个）：
- **Image**: 图像生成和编辑工具
- **Video**: 视频生成和编辑工具
- **Productivity**: 生产力和效率工具
- **Text&Writing**: 文本和写作工具
- **Business**: 商业和企业工具
- **Chatbot**: 聊天机器人和对话工具
- **Design&Art**: 设计和艺术工具
- **Code&IT**: 编程和IT工具
- **Marketing**: 营销和推广工具
- **Prompt**: 提示词和提示工程工具
- **Voice**: 语音和音频工具
- **AI Tools Directory**: AI工具目录
- **AI Detector**: AI检测工具
- **Life Assistant**: 生活助手工具
- **3D**: 3D建模和设计工具
- **Education**: 教育和学习工具
- **Other**: 其他类别

**优势**：
- ✅ LLM基于完整的工具信息（描述、功能、网页内容）进行智能分类
- ✅ 避免了预设映射表可能的偏见和局限性
- ✅ 能够更准确地识别工具的核心功能和用途
- ✅ 支持新兴AI工具类型的自动分类

## 下一步

1. 运行重构验证测试: `python test_refactored.py`
2. 运行功能测试验证效果: `python test_standardizer.py`
3. 根据测试结果调整参数
4. 执行批量处理: `python data_standardizer.py`
5. 将标准化数据导入数据库

---

*创建时间: 2025-01-24*
*版本: v2.0 (重构版)*
