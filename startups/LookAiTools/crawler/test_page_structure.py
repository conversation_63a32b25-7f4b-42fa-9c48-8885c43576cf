#!/usr/bin/env python3
"""
测试页面结构脚本
"""
import asyncio
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup

async def test_page_structure():
    """测试页面结构"""
    target_url = "https://www.toolify.ai/new"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # 设置为False以便观察
        page = await browser.new_page()
        
        try:
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })

            print(f"📱 访问页面: {target_url}")
            await page.goto(target_url, wait_until='domcontentloaded', timeout=60000)
            await page.wait_for_timeout(5000)
            
            # 获取页面内容
            content = await page.content()
            soup = BeautifulSoup(content, 'html.parser')
            
            # 测试不同的选择器
            selectors_to_test = [
                '.grid a[href*="/tool/"]',
                'a[href*="/tool/"]',
                '[href*="/tool/"]',
                '.grid a',
                '.grid',
                'a[href*="tool"]',
                'a[href*="toolify.ai/tool"]',
                '.tool-card',
                '.tool-item',
                '[class*="tool"]',
                '[class*="card"]'
            ]
            
            print("\n🔍 测试不同选择器:")
            for selector in selectors_to_test:
                elements = soup.select(selector)
                print(f"   {selector}: {len(elements)} 个元素")
                
                if elements and len(elements) > 0:
                    print(f"      示例元素: {str(elements[0])[:200]}...")
            
            # 查看页面的主要结构
            print("\n📋 页面主要结构:")
            main_content = soup.select('main, .main, #main, .content, .container')
            if main_content:
                print(f"   主内容区域: {len(main_content)} 个")
                for i, content in enumerate(main_content[:2]):
                    print(f"   内容区域 {i+1}: {content.name} - {content.get('class', [])} - {content.get('id', '')}")
            
            # 查看所有链接
            all_links = soup.select('a[href]')
            tool_links = [link for link in all_links if 'tool' in link.get('href', '').lower()]
            print(f"\n🔗 所有链接: {len(all_links)} 个")
            print(f"🔗 包含'tool'的链接: {len(tool_links)} 个")
            
            if tool_links:
                print("   前5个工具链接:")
                for i, link in enumerate(tool_links[:5]):
                    href = link.get('href', '')
                    text = link.get_text().strip()[:50]
                    print(f"   {i+1}. {href} - {text}")
            
            # 等待用户观察
            print("\n⏳ 等待10秒以便观察页面...")
            await page.wait_for_timeout(10000)
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_page_structure())
