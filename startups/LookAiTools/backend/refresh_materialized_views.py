#!/usr/bin/env python3
"""
定时刷新物化视图脚本
建议通过cron job每小时运行一次
"""

import asyncio
import asyncpg
import time
import os
import logging
from datetime import datetime
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('materialized_view_refresh.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class MaterializedViewRefresher:
    def __init__(self):
        load_dotenv()
        self.DATABASE_URL = os.getenv('DATABASE_URL', 'postgresql://postgres:123456@localhost:5432/lookaitools')
        self.conn = None
    
    async def connect(self):
        """连接数据库"""
        try:
            self.conn = await asyncpg.connect(self.DATABASE_URL)
            logger.info("数据库连接成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    async def close(self):
        """关闭连接"""
        if self.conn:
            await self.conn.close()
            logger.info("数据库连接已关闭")
    
    async def refresh_category_stats(self):
        """刷新分类统计物化视图"""
        try:
            start_time = time.time()
            
            # 检查物化视图是否存在
            exists = await self.conn.fetchval("""
                SELECT EXISTS (
                    SELECT 1 FROM pg_matviews 
                    WHERE matviewname = 'category_stats_mv'
                )
            """)
            
            if not exists:
                logger.warning("物化视图 category_stats_mv 不存在，跳过刷新")
                return False
            
            # 刷新物化视图
            await self.conn.execute("REFRESH MATERIALIZED VIEW CONCURRENTLY category_stats_mv;")
            
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000
            
            logger.info(f"分类统计物化视图刷新完成，耗时: {execution_time:.2f}ms")
            return True
            
        except Exception as e:
            logger.error(f"刷新分类统计物化视图失败: {e}")
            return False
    
    async def get_refresh_stats(self):
        """获取刷新统计信息"""
        try:
            # 获取物化视图的最后刷新时间
            last_refresh = await self.conn.fetchval("""
                SELECT pg_stat_get_last_vacuum_time(oid) 
                FROM pg_class 
                WHERE relname = 'category_stats_mv'
            """)
            
            # 获取物化视图的行数
            row_count = await self.conn.fetchval("SELECT COUNT(*) FROM category_stats_mv")
            
            # 获取原表的行数
            original_count = await self.conn.fetchval("""
                SELECT COUNT(DISTINCT category) 
                FROM ai_tools 
                WHERE status = 'active'
            """)
            
            logger.info(f"物化视图统计:")
            logger.info(f"  最后刷新时间: {last_refresh}")
            logger.info(f"  物化视图行数: {row_count}")
            logger.info(f"  原表分类数: {original_count}")
            
            return {
                "last_refresh": last_refresh,
                "mv_row_count": row_count,
                "original_count": original_count
            }
            
        except Exception as e:
            logger.error(f"获取刷新统计信息失败: {e}")
            return None
    
    async def validate_materialized_view(self):
        """验证物化视图数据的正确性"""
        try:
            # 比较物化视图和原始查询的结果
            mv_result = await self.conn.fetch("""
                SELECT category, count 
                FROM category_stats_mv 
                ORDER BY category
            """)
            
            original_result = await self.conn.fetch("""
                SELECT 
                    category,
                    COUNT(*) as count
                FROM ai_tools 
                WHERE status = 'active'
                GROUP BY category
                ORDER BY category
            """)
            
            # 转换为字典便于比较
            mv_dict = {row['category']: row['count'] for row in mv_result}
            original_dict = {row['category']: row['count'] for row in original_result}
            
            # 检查是否一致
            if mv_dict == original_dict:
                logger.info("物化视图数据验证通过")
                return True
            else:
                logger.warning("物化视图数据与原始数据不一致")
                logger.warning(f"物化视图: {mv_dict}")
                logger.warning(f"原始数据: {original_dict}")
                return False
                
        except Exception as e:
            logger.error(f"验证物化视图失败: {e}")
            return False
    
    async def cleanup_old_logs(self, days=7):
        """清理旧的日志记录"""
        try:
            # 这里可以添加清理逻辑，比如删除旧的日志文件
            # 或者清理数据库中的旧记录
            logger.info(f"清理 {days} 天前的日志记录")
            
        except Exception as e:
            logger.error(f"清理日志失败: {e}")

async def main():
    """主函数"""
    refresher = MaterializedViewRefresher()
    
    try:
        logger.info("开始刷新物化视图任务")
        
        await refresher.connect()
        
        # 获取刷新前的统计信息
        logger.info("=== 刷新前统计 ===")
        await refresher.get_refresh_stats()
        
        # 刷新物化视图
        logger.info("=== 开始刷新 ===")
        success = await refresher.refresh_category_stats()
        
        if success:
            # 获取刷新后的统计信息
            logger.info("=== 刷新后统计 ===")
            await refresher.get_refresh_stats()
            
            # 验证数据正确性
            logger.info("=== 验证数据 ===")
            await refresher.validate_materialized_view()
        
        # 清理旧日志
        await refresher.cleanup_old_logs()
        
        logger.info("物化视图刷新任务完成")
        
    except Exception as e:
        logger.error(f"刷新任务失败: {e}")
        exit(1)
    finally:
        await refresher.close()

if __name__ == "__main__":
    asyncio.run(main())
