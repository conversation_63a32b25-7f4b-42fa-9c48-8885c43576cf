-- 数据库索引优化脚本
-- 针对当前查询模式添加关键索引以提升性能

-- =====================================================
-- 1. 工具表核心索引
-- =====================================================

-- 状态索引 (所有查询都会过滤 status = 'active')
CREATE INDEX IF NOT EXISTS idx_tools_status ON tools(status);

-- 精选工具复合索引 (featured + status + 排序字段)
CREATE INDEX IF NOT EXISTS idx_tools_featured_status_view ON tools(status, featured DESC, view_count DESC, created_at DESC);

-- 分类查询复合索引
CREATE INDEX IF NOT EXISTS idx_tools_category_status ON tools(category_id, status);

-- 浏览量排序索引 (热门工具)
CREATE INDEX IF NOT EXISTS idx_tools_view_count_desc ON tools(view_count DESC) WHERE status = 'active';

-- 创建时间排序索引 (最新工具)
CREATE INDEX IF NOT EXISTS idx_tools_created_desc ON tools(created_at DESC) WHERE status = 'active';

-- =====================================================
-- 2. 工具翻译表索引
-- =====================================================

-- 工具ID和语言复合索引 (JOIN查询的关键)
CREATE INDEX IF NOT EXISTS idx_tool_translations_tool_lang ON tool_translations(tool_id, language_code);

-- 搜索相关的文本索引
CREATE INDEX IF NOT EXISTS idx_tool_translations_name_gin ON tool_translations USING GIN(to_tsvector('english', name));
CREATE INDEX IF NOT EXISTS idx_tool_translations_title_gin ON tool_translations USING GIN(to_tsvector('english', title));
CREATE INDEX IF NOT EXISTS idx_tool_translations_desc_gin ON tool_translations USING GIN(to_tsvector('english', description));

-- =====================================================
-- 3. 分类表索引
-- =====================================================

-- 分类键索引
CREATE INDEX IF NOT EXISTS idx_categories_key ON categories(category_key);

-- 分类翻译表索引
CREATE INDEX IF NOT EXISTS idx_category_translations_cat_lang ON category_translations(category_id, language_code);

-- =====================================================
-- 4. 标签相关索引 (如果使用)
-- =====================================================

-- 工具标签关联索引
CREATE INDEX IF NOT EXISTS idx_tool_tags_tool_id ON tool_tags(tool_id) WHERE EXISTS (SELECT 1 FROM tool_tags);
CREATE INDEX IF NOT EXISTS idx_tool_tags_tag_id ON tool_tags(tag_id) WHERE EXISTS (SELECT 1 FROM tool_tags);

-- =====================================================
-- 5. 部分索引优化 (只索引活跃数据)
-- =====================================================

-- 只为活跃工具创建的精选索引
CREATE INDEX IF NOT EXISTS idx_tools_active_featured ON tools(featured DESC, view_count DESC) 
WHERE status = 'active';

-- 只为活跃工具创建的分类索引
CREATE INDEX IF NOT EXISTS idx_tools_active_category ON tools(category_id, view_count DESC) 
WHERE status = 'active';

-- =====================================================
-- 6. 统计信息更新
-- =====================================================

-- 更新表统计信息以优化查询计划
ANALYZE tools;
ANALYZE tool_translations;
ANALYZE categories;
ANALYZE category_translations;

-- =====================================================
-- 7. 查询性能验证
-- =====================================================

-- 验证索引是否被使用的示例查询
-- EXPLAIN ANALYZE SELECT t.*, tt.name, tt.title, tt.description 
-- FROM tools t 
-- LEFT JOIN tool_translations tt ON t.id = tt.tool_id AND tt.language_code = 'en'
-- WHERE t.status = 'active' AND t.featured = true
-- ORDER BY t.view_count DESC, t.created_at DESC 
-- LIMIT 8;

-- 显示当前表的索引信息
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('tools', 'tool_translations', 'categories', 'category_translations')
ORDER BY tablename, indexname;
