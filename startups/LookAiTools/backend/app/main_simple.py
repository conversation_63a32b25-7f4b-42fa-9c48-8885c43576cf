#!/usr/bin/env python3
"""
简化的后端API服务器 - 用于测试
不依赖数据库连接，返回模拟数据
"""

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

app = FastAPI(
    title="AI Tools Navigator API - Simple",
    description="简化的AI工具导航站后端API",
    version="2.0.0-simple"
)

# 添加gzip压缩中间件
app.add_middleware(GZipMiddleware, minimum_size=1000)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic模型
class APIResponse(BaseModel):
    data: Any
    pagination: Optional[Dict] = None
    success: bool = True
    message: Optional[str] = None

# 模拟数据
MOCK_CATEGORIES = [
    {"id": "productivity", "name": "Productivity", "slug": "productivity", "description": "Tools to enhance your productivity and efficiency", "count": 126},
    {"id": "ai-tools", "name": "AI Tools", "slug": "ai-tools", "description": "Artificial Intelligence powered applications", "count": 2},
    {"id": "design", "name": "Design", "slug": "design", "description": "Creative design and visual tools", "count": 3},
    {"id": "chatbot", "name": "Chatbot", "slug": "chatbot", "description": "AI chatbot and conversation tools", "count": 0},
    {"id": "image", "name": "Image", "slug": "image", "description": "AI image generation and editing tools", "count": 0},
]

MOCK_CATEGORIES_CN = [
    {"id": "productivity", "name": "生产力工具", "slug": "productivity", "description": "提升工作效率和生产力的工具", "count": 126},
    {"id": "ai-tools", "name": "AI工具", "slug": "ai-tools", "description": "人工智能驱动的应用程序", "count": 2},
    {"id": "design", "name": "设计工具", "slug": "design", "description": "创意设计和视觉工具", "count": 3},
    {"id": "chatbot", "name": "聊天机器人", "slug": "chatbot", "description": "AI聊天机器人和对话工具", "count": 0},
    {"id": "image", "name": "图像工具", "slug": "image", "description": "AI图像生成和编辑工具", "count": 0},
]

MOCK_TOOLS_EN = [
    {
        "id": 1,
        "slug": "seedance-ai",
        "name": "SEEDANCE AI",
        "title": "Seedance AI Video Generator - Professional Videos",
        "description": "Professional AI video generation tool supporting text-to-video and image-to-video conversion with cinematic camera movements and multi-shot storytelling capabilities for high-quality video content.",
        "url": "https://seedanceai.net",
        "thumbnail_url": "https://lookforai.s3.us-east-1.amazonaws.com/aitools/Seedance_Ai_Video_Generator_Professional_Videos.jpg",
        "category": "productivity",
        "category_name": "Productivity",
        "pricing_type": "paid",
        "rating": 4.5,
        "view_count": 1000,
        "featured": False,
        "tags": ["Text to Video", "Image to Video", "Short Video Generation"],
        "created_at": "2025-07-26 08:25:10.148212+00:00",
        "trial_available": False,
        "traffic_estimate": 50000
    },
    {
        "id": 2,
        "slug": "sim-studio",
        "name": "SIM STUDIO",
        "title": "Sim Studio AI Agent Workflow Platform",
        "description": "A Figma-like visual AI agent building platform that offers drag-and-drop interface to create and deploy AI agent workflows, enabling developers to easily build automation processes without complex coding.",
        "url": "https://www.simstudio.ai",
        "thumbnail_url": "https://lookforai.s3.us-east-1.amazonaws.com/aitools/Sim_Studio.jpg",
        "category": "productivity",
        "category_name": "Productivity",
        "pricing_type": "freemium",
        "rating": 4.2,
        "view_count": 800,
        "featured": True,
        "tags": ["AI Agent", "No-Code", "Workflow Automation"],
        "created_at": "2025-07-26 08:25:00.185480+00:00",
        "trial_available": True,
        "traffic_estimate": 30000
    }
]

MOCK_TOOLS_CN = [
    {
        "id": 1,
        "slug": "seedance-ai",
        "name": "Seedance AI视频生成器",
        "title": "Seedance AI视频生成器 - 专业视频制作",
        "description": "专业的AI视频生成工具，支持文本转视频和图像转视频，具备电影级镜头移动效果和多镜头叙事功能，可生成高质量的专业视频内容。",
        "url": "https://seedanceai.net",
        "thumbnail_url": "https://lookforai.s3.us-east-1.amazonaws.com/aitools/Seedance_Ai_Video_Generator_Professional_Videos.jpg",
        "category": "productivity",
        "category_name": "生产力工具",
        "pricing_type": "paid",
        "rating": 4.5,
        "view_count": 1000,
        "featured": False,
        "tags": ["文本转视频", "图像转视频", "短视频生成"],
        "created_at": "2025-07-26 08:25:10.148212+00:00",
        "trial_available": False,
        "traffic_estimate": 50000
    },
    {
        "id": 2,
        "slug": "sim-studio",
        "name": "SIM STUDIO",
        "title": "Sim Studio AI代理工作流平台",
        "description": "一个类Figma的可视化AI代理构建平台，提供拖拽式界面来创建和部署AI代理工作流，让开发者能够轻松构建自动化流程而无需复杂编码。",
        "url": "https://www.simstudio.ai",
        "thumbnail_url": "https://lookforai.s3.us-east-1.amazonaws.com/aitools/Sim_Studio.jpg",
        "category": "productivity",
        "category_name": "生产力工具",
        "pricing_type": "freemium",
        "rating": 4.2,
        "view_count": 800,
        "featured": True,
        "tags": ["AI代理", "无代码", "工作流自动化"],
        "created_at": "2025-07-26 08:25:00.185480+00:00",
        "trial_available": True,
        "traffic_estimate": 30000
    }
]

# API路由
@app.get("/")
async def root():
    """根路径"""
    return {"message": "AI Tools Navigator API - Simple", "version": "2.0.0-simple"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "database": "mock", "architecture": "simple"}

@app.get("/api/categories")
async def get_categories(language: str = Query("en", description="语言")):
    """获取分类列表"""
    categories = MOCK_CATEGORIES_CN if language == "cn" else MOCK_CATEGORIES
    return APIResponse(data=categories)

@app.get("/api/subcategories")
async def get_subcategories(language: str = Query("en", description="语言")):
    """获取子分类列表 - 兼容性端点"""
    return APIResponse(data=[])

@app.get("/api/tags")
async def get_tags(language: str = Query("en", description="语言")):
    """获取标签列表"""
    tags = [
        {"id": "ai-agent", "name": "AI Agent" if language == "en" else "AI代理", "slug": "ai-agent", "count": 10},
        {"id": "no-code", "name": "No-Code" if language == "en" else "无代码", "slug": "no-code", "count": 8},
        {"id": "video-generation", "name": "Video Generation" if language == "en" else "视频生成", "slug": "video-generation", "count": 5}
    ]
    return APIResponse(data=tags)

@app.get("/api/tools")
async def get_tools(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(12, ge=1, le=100, description="每页数量"),
    category: Optional[str] = Query(None, description="分类筛选"),
    featured: Optional[str] = Query(None, description="是否精选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    language: str = Query("en", description="语言"),
    minimal: Optional[str] = Query(None, description="简化响应")
):
    """获取工具列表"""
    tools = MOCK_TOOLS_CN if language == "cn" else MOCK_TOOLS_EN
    
    # 应用筛选
    filtered_tools = tools.copy()
    
    if category:
        filtered_tools = [t for t in filtered_tools if t["category"] == category]
    
    if featured and featured.lower() in ('true', '1', 'yes'):
        filtered_tools = [t for t in filtered_tools if t["featured"]]
    
    if search:
        search_lower = search.lower()
        filtered_tools = [t for t in filtered_tools if 
                         search_lower in t["name"].lower() or 
                         search_lower in t["description"].lower()]
    
    # 分页
    total = len(filtered_tools)
    start = (page - 1) * limit
    end = start + limit
    paginated_tools = filtered_tools[start:end]
    
    total_pages = (total + limit - 1) // limit
    
    return APIResponse(
        data=paginated_tools,
        pagination={
            "page": page,
            "limit": limit,
            "total": total,
            "total_pages": total_pages
        }
    )

@app.get("/api/tools/{tool_identifier}")
async def get_tool(tool_identifier: str, language: str = Query("en", description="语言")):
    """获取单个工具详情"""
    tools = MOCK_TOOLS_CN if language == "cn" else MOCK_TOOLS_EN
    
    # 查找工具
    tool = None
    for t in tools:
        if t["slug"] == tool_identifier or str(t["id"]) == tool_identifier:
            tool = t.copy()
            break
    
    if not tool:
        raise HTTPException(status_code=404, detail="工具不存在")
    
    # 添加详细信息
    tool.update({
        "long_description": tool["description"] + " This is additional detailed information about the tool.",
        "use_cases": "Content creation, marketing, education" if language == "en" else "内容创作、营销、教育",
        "target_audience": "Content creators, marketers, developers" if language == "en" else "内容创作者、营销人员、开发者",
        "subcategory": "AI Video Tools" if language == "en" else "AI视频工具",
        "industry_tags": ["Technology", "Media"] if language == "en" else ["技术", "媒体"],
        "key_features": ["High quality output", "Fast processing", "Easy to use"] if language == "en" else ["高质量输出", "快速处理", "易于使用"]
    })
    
    return APIResponse(data=tool)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
