#!/usr/bin/env python3
"""
AI工具导航站后端API - 多语言架构版本
FastAPI应用主入口，适配新的多语言数据库架构
"""

from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import asyncpg
import json
import os
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, EmailStr
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

app = FastAPI(
    title="AI Tools Navigator API - Multilingual",
    description="AI工具导航站后端API - 多语言架构版本",
    version="2.0.0"
)

# 添加gzip压缩中间件
app.add_middleware(GZipMiddleware, minimum_size=1000)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据库连接池
db_pool = None

# Pydantic模型
class BilingualText(BaseModel):
    en: str
    cn: str

class ToolResponse(BaseModel):
    id: int
    slug: str
    name: str
    title: str
    description: str
    url: str
    thumbnail_url: Optional[str] = None
    category: str
    category_name: str
    pricing_type: str
    rating: float
    view_count: int
    featured: bool
    tags: List[str] = []
    created_at: str

class CategoryResponse(BaseModel):
    id: str
    name: str
    slug: str
    description: Optional[str] = None
    count: int

class PaginationResponse(BaseModel):
    page: int
    limit: int
    total: int
    totalPages: int

class APIResponse(BaseModel):
    data: Any
    pagination: Optional[PaginationResponse] = None
    success: bool = True
    message: Optional[str] = None

# 数据库连接管理
async def get_db_connection():
    """获取数据库连接"""
    global db_pool
    if db_pool is None:
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            raise HTTPException(status_code=500, detail="数据库连接配置错误")

        try:
            db_pool = await asyncpg.create_pool(
                database_url,
                min_size=2,
                max_size=10,
                max_queries=50000,
                max_inactive_connection_lifetime=300,
                command_timeout=60,
                server_settings={
                    'application_name': 'lookaitools_api_v2',
                    'jit': 'off'
                }
            )
            print("✓ 数据库连接池创建成功")
        except Exception as e:
            print(f"✗ 数据库连接失败: {e}")
            raise
    return db_pool

async def close_db_connection():
    """关闭数据库连接"""
    global db_pool
    if db_pool:
        await db_pool.close()

# 启动和关闭事件
@app.on_event("startup")
async def startup_event():
    """应用启动时初始化数据库连接"""
    await get_db_connection()
    print("✓ 数据库连接池已初始化 (多语言架构)")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    await close_db_connection()
    print("✓ 数据库连接池已关闭")

# 工具函数
def normalize_language_code(language: str) -> str:
    """标准化语言代码"""
    language_mapping = {
        'zh': 'cn',
        'zh-CN': 'cn',
        'zh-Hans': 'cn',
        'chinese': 'cn',
        'cn': 'cn',
        'en': 'en',
        'english': 'en'
    }
    return language_mapping.get(language.lower(), 'en')

def format_tool_response(tool_row: dict, language: str = "en") -> dict:
    """格式化工具响应数据 - 多语言架构版本"""
    
    # 根据语言选择字段
    name_field = 'name' if language == 'en' else 'name'  # 从翻译表获取
    title_field = 'title' if language == 'en' else 'title'
    description_field = 'description' if language == 'en' else 'description'
    category_name_field = 'category_name' if language == 'en' else 'category_name'
    
    return {
        "id": tool_row.get('id'),
        "slug": tool_row.get('slug', ''),
        "name": tool_row.get(name_field, ''),
        "title": tool_row.get(title_field, ''),
        "description": tool_row.get(description_field, ''),
        "url": tool_row.get('url', ''),
        "thumbnail_url": tool_row.get('page_screenshot', ''),
        "category": tool_row.get('category_key', ''),
        "category_name": tool_row.get(category_name_field, ''),
        "pricing_type": tool_row.get('pricing_type', 'freemium'),
        "rating": float(tool_row.get('rating', 0)),
        "view_count": tool_row.get('view_count', 0),
        "featured": tool_row.get('featured', False),
        "tags": tool_row.get('tags', []),
        "created_at": str(tool_row.get('created_at', '')),
        "trial_available": tool_row.get('trial_available', False),
        "traffic_estimate": tool_row.get('traffic_estimate', 0)
    }

# API路由
@app.get("/")
async def root():
    """根路径"""
    return {"message": "AI Tools Navigator API - Multilingual", "version": "2.0.0"}

@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        pool = await get_db_connection()
        async with pool.acquire() as conn:
            await conn.fetchval("SELECT 1")
        return {"status": "healthy", "database": "connected", "architecture": "multilingual"}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}

@app.get("/api/tools")
async def get_tools(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(12, ge=1, le=100, description="每页数量"),
    category: Optional[str] = Query(None, description="分类筛选"),
    tags: Optional[str] = Query(None, description="标签筛选"),
    featured: Optional[str] = Query(None, description="是否精选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    language: str = Query("en", description="语言"),
    minimal: Optional[str] = Query(None, description="简化响应"),
    all: bool = Query(False, description="是否返回所有数据（忽略分页）")
):
    """获取工具列表 - 多语言架构版本"""
    try:
        # 标准化语言代码
        language = normalize_language_code(language)
        pool = await get_db_connection()
        async with pool.acquire() as conn:
            # 构建查询条件
            where_conditions = ["t.status = 'active'"]
            params = []
            param_count = 1  # 从1开始，因为$1是language参数

            if category:
                param_count += 1
                where_conditions.append(f"c.category_key = ${param_count}")
                params.append(category)

            if featured is not None:
                param_count += 1
                where_conditions.append(f"t.featured = ${param_count}")
                # 转换字符串为布尔值
                featured_bool = featured.lower() in ('true', '1', 'yes') if isinstance(featured, str) else bool(featured)
                params.append(featured_bool)

            if tags:
                # 处理标签筛选，支持多个标签
                tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
                if tag_list:
                    param_count += 1
                    # 使用EXISTS子查询来筛选包含指定标签的工具
                    # 支持通过标签名或标签slug进行筛选
                    where_conditions.append(f"""
                        EXISTS (
                            SELECT 1 FROM tool_tags tt_inner
                            JOIN tag_translations ttr ON tt_inner.tag_id = ttr.tag_id
                            JOIN tags tg ON tt_inner.tag_id = tg.id
                            WHERE tt_inner.tool_id = t.id
                            AND ttr.language_code = $1
                            AND (ttr.tag_name = ANY(${param_count}) OR tg.tag_key = ANY(${param_count}))
                        )
                    """)
                    params.append(tag_list)

            if search:
                param_count += 1
                where_conditions.append(f"""
                    (tt.name ILIKE ${param_count} OR
                     tt.title ILIKE ${param_count} OR
                     tt.description ILIKE ${param_count})
                """)
                params.append(f"%{search}%")

            where_clause = " AND ".join(where_conditions)

            # 构建基础查询 - 使用新的多语言表结构
            base_query = f"""
                FROM tools t
                JOIN categories c ON t.category_id = c.id
                JOIN tool_translations tt ON t.id = tt.tool_id AND tt.language_code = $1
                JOIN category_translations ct ON c.id = ct.category_id AND ct.language_code = $1
                WHERE {where_clause}
            """

            # 将语言参数添加到参数列表开头
            all_params = [language] + params

            # 获取总数
            count_query = f"SELECT COUNT(DISTINCT t.id) {base_query}"
            total = await conn.fetchval(count_query, *all_params)

            # 获取数据
            if all:
                # 返回所有数据，不分页
                query = f"""
                    SELECT DISTINCT
                        t.id, t.slug, t.url, t.page_screenshot, t.pricing_type,
                        t.rating, t.view_count, t.traffic_estimate, t.featured,
                        t.trial_available, t.created_at,
                        c.category_key,
                        ct.category_name,
                        tt.name, tt.title, tt.description
                    {base_query}
                    ORDER BY t.featured DESC, t.view_count DESC, t.created_at DESC
                """
            else:
                # 分页查询
                offset = (page - 1) * limit
                limit_param = param_count + 1
                offset_param = param_count + 2

                query = f"""
                    SELECT DISTINCT
                        t.id, t.slug, t.url, t.page_screenshot, t.pricing_type,
                        t.rating, t.view_count, t.traffic_estimate, t.featured,
                        t.trial_available, t.created_at,
                        c.category_key,
                        ct.category_name,
                        tt.name, tt.title, tt.description
                    {base_query}
                    ORDER BY t.featured DESC, t.view_count DESC, t.created_at DESC
                    LIMIT ${limit_param} OFFSET ${offset_param}
                """
                all_params.extend([limit, offset])

            rows = await conn.fetch(query, *all_params)

            # 获取每个工具的标签
            tools = []
            for row in rows:
                tool_data = dict(row)
                
                # 获取标签
                tags_query = """
                    SELECT ttr.tag_name
                    FROM tool_tags tt
                    JOIN tag_translations ttr ON tt.tag_id = ttr.tag_id
                    WHERE tt.tool_id = $1 AND ttr.language_code = $2 AND tt.tag_type = 'general'
                    LIMIT 5
                """
                tag_rows = await conn.fetch(tags_query, row['id'], language)
                tool_data['tags'] = [tag['tag_name'] for tag in tag_rows]

                tools.append(format_tool_response(tool_data, language))

            if all:
                # 返回所有数据时，分页信息特殊处理
                return APIResponse(
                    data=tools,
                    pagination=PaginationResponse(
                        page=1,
                        limit=total,  # limit设为总数
                        total=total,
                        totalPages=1  # 只有1页
                    )
                )
            else:
                # 正常分页
                total_pages = (total + limit - 1) // limit
                return APIResponse(
                    data=tools,
                    pagination=PaginationResponse(
                        page=page,
                        limit=limit,
                        total=total,
                        totalPages=total_pages
                    )
                )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工具列表失败: {str(e)}")

@app.get("/api/tools/{tool_identifier}")
async def get_tool(tool_identifier: str, language: str = Query("en", description="语言")):
    """获取单个工具详情 - 多语言架构版本"""
    try:
        # 标准化语言代码
        language = normalize_language_code(language)
        pool = await get_db_connection()
        async with pool.acquire() as conn:
            # 构建查询
            base_query = """
                SELECT 
                    t.*, 
                    c.category_key,
                    ct.category_name, ct.category_description,
                    tt.name, tt.title, tt.description, tt.long_description,
                    tt.use_cases, tt.target_audience, tt.subcategory
                FROM tools t
                LEFT JOIN categories c ON t.category_id = c.id
                LEFT JOIN tool_translations tt ON t.id = tt.tool_id AND tt.language_code = $1
                LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.language_code = $1
                WHERE t.status = 'active'
            """

            # 尝试通过slug或ID查找
            if tool_identifier.isdigit():
                query = base_query + " AND t.id = $2"
                row = await conn.fetchrow(query, language, int(tool_identifier))
            else:
                query = base_query + " AND t.slug = $2"
                row = await conn.fetchrow(query, language, tool_identifier)

            if not row:
                raise HTTPException(status_code=404, detail="工具不存在")

            # 获取标签
            tags_query = """
                SELECT ttr.tag_name, tt.tag_type
                FROM tool_tags tt
                JOIN tag_translations ttr ON tt.tag_id = ttr.tag_id
                WHERE tt.tool_id = $1 AND ttr.language_code = $2
            """
            tags = await conn.fetch(tags_query, row['id'], language)

            # 获取功能特性
            features_query = """
                SELECT feature_text
                FROM tool_features
                WHERE tool_id = $1 AND language_code = $2
                ORDER BY sort_order
            """
            features = await conn.fetch(features_query, row['id'], language)

            # 格式化工具数据
            tool_data = dict(row)
            tool_data['tags'] = [tag['tag_name'] for tag in tags if tag['tag_type'] == 'general']
            tool_data['industry_tags'] = [tag['tag_name'] for tag in tags if tag['tag_type'] == 'industry']
            tool_data['key_features'] = [f['feature_text'] for f in features]

            # 构建完整响应
            response_data = format_tool_response(tool_data, language)
            response_data.update({
                "long_description": tool_data.get('long_description', ''),
                "use_cases": tool_data.get('use_cases', ''),
                "target_audience": tool_data.get('target_audience', ''),
                "subcategory": tool_data.get('subcategory', ''),
                "industry_tags": tool_data.get('industry_tags', []),
                "key_features": tool_data.get('key_features', []),
                "category_description": tool_data.get('category_description', '')
            })

            return APIResponse(data=response_data)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工具详情失败: {str(e)}")

@app.get("/api/categories")
async def get_categories(language: str = Query("en", description="语言")):
    """获取分类列表 - 多语言架构版本"""
    try:
        # 标准化语言代码
        language = normalize_language_code(language)
        pool = await get_db_connection()
        async with pool.acquire() as conn:
            query = """
                SELECT 
                    c.category_key,
                    ct.category_name,
                    ct.category_description,
                    COUNT(t.id) as tool_count
                FROM categories c
                LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.language_code = $1
                LEFT JOIN tools t ON c.id = t.category_id AND t.status = 'active'
                GROUP BY c.id, c.category_key, ct.category_name, ct.category_description, c.sort_order
                ORDER BY c.sort_order, tool_count DESC
            """
            rows = await conn.fetch(query, language)

            categories = []
            for row in rows:
                categories.append({
                    "id": row['category_key'],
                    "name": row['category_name'] or row['category_key'],
                    "slug": row['category_key'],
                    "description": row['category_description'] or '',
                    "count": row['tool_count']
                })

            return APIResponse(data=categories)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类列表失败: {str(e)}")

@app.get("/api/subcategories")
async def get_subcategories(language: str = Query("en", description="语言")):
    """获取子分类列表 - 兼容性端点"""
    # 返回空数组，因为新架构中没有独立的子分类
    return APIResponse(data=[])

@app.get("/api/tags")
async def get_tags(language: str = Query("en", description="语言")):
    """获取标签列表 - 多语言架构版本"""
    try:
        # 标准化语言代码
        language = normalize_language_code(language)
        pool = await get_db_connection()
        async with pool.acquire() as conn:
            query = """
                SELECT
                    t.tag_key as id,
                    tt.tag_name as name,
                    t.tag_key as slug,
                    COUNT(tool_tags.tool_id) as count
                FROM tags t
                LEFT JOIN tag_translations tt ON t.id = tt.tag_id AND tt.language_code = $1
                LEFT JOIN tool_tags ON t.id = tool_tags.tag_id
                LEFT JOIN tools ON tool_tags.tool_id = tools.id AND tools.status = 'active'
                GROUP BY t.id, t.tag_key, tt.tag_name
                HAVING COUNT(tool_tags.tool_id) > 0
                ORDER BY COUNT(tool_tags.tool_id) DESC, tt.tag_name
                LIMIT 50
            """
            rows = await conn.fetch(query, language)

            tags = []
            for row in rows:
                tags.append({
                    "id": row['id'],
                    "name": row['name'] or row['id'],
                    "slug": row['slug'],
                    "count": row['count']
                })

            return APIResponse(data=tags)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取标签列表失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)  # 使用不同端口避免冲突
