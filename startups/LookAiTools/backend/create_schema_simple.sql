-- 简化的多语言数据库架构
-- 删除旧表并创建新的多语言表结构

-- =====================================================
-- 删除旧表 (如果存在)
-- =====================================================
DROP TABLE IF EXISTS pricing_plan_features CASCADE;
DROP TABLE IF EXISTS pricing_plans CASCADE;
DROP TABLE IF EXISTS tool_tags CASCADE;
DROP TABLE IF EXISTS tag_translations CASCADE;
DROP TABLE IF EXISTS tags CASCADE;
DROP TABLE IF EXISTS tool_features CASCADE;
DROP TABLE IF EXISTS tool_translations CASCADE;
DROP TABLE IF EXISTS tools CASCADE;
DROP TABLE IF EXISTS category_translations CASCADE;
DROP TABLE IF EXISTS categories CASCADE;
DROP TABLE IF EXISTS tool_metadata CASCADE;

-- 删除视图
DROP VIEW IF EXISTS v_tool_details CASCADE;
DROP VIEW IF EXISTS v_tool_search CASCADE;
DROP VIEW IF EXISTS v_categories CASCADE;

-- =====================================================
-- 1. 分类表
-- =====================================================
CREATE TABLE categories (
    id BIGSERIAL PRIMARY KEY,
    category_key VARCHAR(50) UNIQUE NOT NULL,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 2. 分类多语言表
-- =====================================================
CREATE TABLE category_translations (
    id BIGSERIAL PRIMARY KEY,
    category_id BIGINT NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL,
    category_name VARCHAR(100) NOT NULL,
    category_description TEXT,
    
    UNIQUE(category_id, language_code)
);

-- =====================================================
-- 3. 主工具表
-- =====================================================
CREATE TABLE tools (
    id BIGSERIAL PRIMARY KEY,
    slug VARCHAR(100) UNIQUE NOT NULL,
    url TEXT NOT NULL,
    page_screenshot TEXT,
    category_id BIGINT NOT NULL REFERENCES categories(id),
    pricing_type VARCHAR(20) NOT NULL CHECK (pricing_type IN ('free', 'paid', 'freemium')),
    trial_available BOOLEAN DEFAULT FALSE,
    rating DECIMAL(3,2) DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
    view_count BIGINT DEFAULT 0,
    traffic_estimate BIGINT DEFAULT 0,
    featured BOOLEAN DEFAULT FALSE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_slug UNIQUE (slug)
);

-- =====================================================
-- 4. 工具多语言信息表
-- =====================================================
CREATE TABLE tool_translations (
    id BIGSERIAL PRIMARY KEY,
    tool_id BIGINT NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL,
    name VARCHAR(200) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    long_description TEXT,
    use_cases TEXT,
    target_audience TEXT,
    subcategory VARCHAR(100),
    
    UNIQUE(tool_id, language_code)
);

-- =====================================================
-- 5. 功能特性表
-- =====================================================
CREATE TABLE tool_features (
    id BIGSERIAL PRIMARY KEY,
    tool_id BIGINT NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL,
    feature_text TEXT NOT NULL,
    sort_order INTEGER DEFAULT 0
);

-- =====================================================
-- 6. 标签表
-- =====================================================
CREATE TABLE tags (
    id BIGSERIAL PRIMARY KEY,
    tag_key VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 7. 标签多语言表
-- =====================================================
CREATE TABLE tag_translations (
    id BIGSERIAL PRIMARY KEY,
    tag_id BIGINT NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL,
    tag_name VARCHAR(100) NOT NULL,
    
    UNIQUE(tag_id, language_code)
);

-- =====================================================
-- 8. 工具标签关联表
-- =====================================================
CREATE TABLE tool_tags (
    tool_id BIGINT NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    tag_id BIGINT NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    tag_type VARCHAR(20) NOT NULL CHECK (tag_type IN ('general', 'industry')),
    
    PRIMARY KEY (tool_id, tag_id)
);

-- =====================================================
-- 9. 元数据表
-- =====================================================
CREATE TABLE tool_metadata (
    tool_id BIGINT PRIMARY KEY REFERENCES tools(id) ON DELETE CASCADE,
    source_site VARCHAR(100),
    crawl_timestamp TIMESTAMP WITH TIME ZONE,
    processing_timestamp TIMESTAMP WITH TIME ZONE,
    model_input_data TEXT
);

-- ===============================
-- 索引优化
-- ===============================
CREATE INDEX idx_tools_category ON tools(category_id);
CREATE INDEX idx_tools_pricing_type ON tools(pricing_type);
CREATE INDEX idx_categories_key ON categories(category_key);
CREATE INDEX idx_tools_featured ON tools(featured);
CREATE INDEX idx_tools_status ON tools(status);
CREATE INDEX idx_tools_rating ON tools(rating DESC);
CREATE INDEX idx_tools_view_count ON tools(view_count DESC);

-- 多语言查询索引
CREATE INDEX idx_tool_translations_lang ON tool_translations(language_code);
CREATE INDEX idx_tool_translations_tool_lang ON tool_translations(tool_id, language_code);
CREATE INDEX idx_category_translations_lang ON category_translations(language_code);
CREATE INDEX idx_category_translations_cat_lang ON category_translations(category_id, language_code);

-- 全文搜索索引
CREATE INDEX idx_tool_translations_name_gin ON tool_translations USING gin(to_tsvector('english', name));
CREATE INDEX idx_tool_translations_title_gin ON tool_translations USING gin(to_tsvector('english', title));
CREATE INDEX idx_tool_translations_desc_gin ON tool_translations USING gin(to_tsvector('english', description));

-- 标签查询索引
CREATE INDEX idx_tool_tags_tool ON tool_tags(tool_id);
CREATE INDEX idx_tool_tags_tag ON tool_tags(tag_id);
CREATE INDEX idx_tool_tags_type ON tool_tags(tag_type);

-- ===============================
-- 更新时间戳触发器
-- ===============================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_tools_updated_at BEFORE UPDATE ON tools
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===============================
-- 插入基础分类数据
-- ===============================
INSERT INTO categories (category_key, sort_order) VALUES 
('productivity', 1),
('ai-tools', 2),
('design', 3),
('chatbot', 4),
('image', 5),
('video', 6),
('code-it', 7),
('business', 8),
('marketing', 9),
('text-writing', 10),
('voice', 11),
('education', 12),
('life-assistant', 13),
('other', 14);

-- 插入分类翻译
INSERT INTO category_translations (category_id, language_code, category_name, category_description) 
SELECT 
    c.id,
    'en',
    CASE c.category_key
        WHEN 'productivity' THEN 'Productivity'
        WHEN 'ai-tools' THEN 'AI Tools'
        WHEN 'design' THEN 'Design'
        WHEN 'chatbot' THEN 'Chatbot'
        WHEN 'image' THEN 'Image'
        WHEN 'video' THEN 'Video'
        WHEN 'code-it' THEN 'Code & IT'
        WHEN 'business' THEN 'Business'
        WHEN 'marketing' THEN 'Marketing'
        WHEN 'text-writing' THEN 'Text & Writing'
        WHEN 'voice' THEN 'Voice'
        WHEN 'education' THEN 'Education'
        WHEN 'life-assistant' THEN 'Life Assistant'
        ELSE 'Other'
    END,
    CASE c.category_key
        WHEN 'productivity' THEN 'Tools to enhance your productivity and efficiency'
        WHEN 'ai-tools' THEN 'Artificial Intelligence powered applications'
        WHEN 'design' THEN 'Creative design and visual tools'
        WHEN 'chatbot' THEN 'AI chatbot and conversation tools'
        WHEN 'image' THEN 'AI image generation and editing tools'
        WHEN 'video' THEN 'AI video generation and editing tools'
        WHEN 'code-it' THEN 'AI programming and development tools'
        WHEN 'business' THEN 'AI business and enterprise tools'
        WHEN 'marketing' THEN 'AI marketing and promotion tools'
        WHEN 'text-writing' THEN 'AI writing and content creation tools'
        WHEN 'voice' THEN 'AI voice and audio tools'
        WHEN 'education' THEN 'AI education and learning tools'
        WHEN 'life-assistant' THEN 'AI life and personal assistant tools'
        ELSE 'Other AI tools and utilities'
    END
FROM categories c;

INSERT INTO category_translations (category_id, language_code, category_name, category_description) 
SELECT 
    c.id,
    'cn',
    CASE c.category_key
        WHEN 'productivity' THEN '生产力工具'
        WHEN 'ai-tools' THEN 'AI工具'
        WHEN 'design' THEN '设计工具'
        WHEN 'chatbot' THEN '聊天机器人'
        WHEN 'image' THEN '图像工具'
        WHEN 'video' THEN '视频工具'
        WHEN 'code-it' THEN '编程IT'
        WHEN 'business' THEN '商业工具'
        WHEN 'marketing' THEN '营销工具'
        WHEN 'text-writing' THEN '文本写作'
        WHEN 'voice' THEN '语音工具'
        WHEN 'education' THEN '教育工具'
        WHEN 'life-assistant' THEN '生活助手'
        ELSE '其他'
    END,
    CASE c.category_key
        WHEN 'productivity' THEN '提升工作效率和生产力的工具'
        WHEN 'ai-tools' THEN '人工智能驱动的应用程序'
        WHEN 'design' THEN '创意设计和视觉工具'
        WHEN 'chatbot' THEN 'AI聊天机器人和对话工具'
        WHEN 'image' THEN 'AI图像生成和编辑工具'
        WHEN 'video' THEN 'AI视频生成和编辑工具'
        WHEN 'code-it' THEN 'AI编程和开发工具'
        WHEN 'business' THEN 'AI商业和企业工具'
        WHEN 'marketing' THEN 'AI营销和推广工具'
        WHEN 'text-writing' THEN 'AI写作和内容创作工具'
        WHEN 'voice' THEN 'AI语音和音频工具'
        WHEN 'education' THEN 'AI教育和学习工具'
        WHEN 'life-assistant' THEN 'AI生活和个人助手工具'
        ELSE '其他AI工具和实用程序'
    END
FROM categories c;
