-- 工具门户数据库设计
-- 支持多语言、分类、搜索、评级等功能

-- =====================================================
-- 删除旧表 (如果存在)
-- =====================================================
DROP TABLE IF EXISTS pricing_plan_features CASCADE;
DROP TABLE IF EXISTS pricing_plans CASCADE;
DROP TABLE IF EXISTS tool_tags CASCADE;
DROP TABLE IF EXISTS tag_translations CASCADE;
DROP TABLE IF EXISTS tags CASCADE;
DROP TABLE IF EXISTS tool_features CASCADE;
DROP TABLE IF EXISTS tool_translations CASCADE;
DROP TABLE IF EXISTS tools CASCADE;
DROP TABLE IF EXISTS category_translations CASCADE;
DROP TABLE IF EXISTS categories CASCADE;
DROP TABLE IF EXISTS tool_metadata CASCADE;

-- 删除视图
DROP VIEW IF EXISTS v_tool_details CASCADE;
DROP VIEW IF EXISTS v_tool_search CASCADE;
DROP VIEW IF EXISTS v_categories CASCADE;

-- =====================================================
-- 1. 分类表
-- =====================================================
CREATE TABLE categories (
    id BIGSERIAL PRIMARY KEY,
    category_key VARCHAR(50) UNIQUE NOT NULL, -- 用于关联的唯一标识，如 'productivity'
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 2. 分类多语言表
-- =====================================================
CREATE TABLE category_translations (
    id BIGSERIAL PRIMARY KEY,
    category_id BIGINT NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL,
    category_name VARCHAR(100) NOT NULL,
    category_description TEXT,

    UNIQUE(category_id, language_code)
);

-- =====================================================
-- 3. 主工具表
-- =====================================================
CREATE TABLE tools (
    id BIGSERIAL PRIMARY KEY,
    slug VARCHAR(100) UNIQUE NOT NULL,
    url TEXT NOT NULL,
    page_screenshot TEXT,
    category_id BIGINT NOT NULL REFERENCES categories(id),
    pricing_type VARCHAR(20) NOT NULL CHECK (pricing_type IN ('free', 'paid', 'freemium')),
    trial_available BOOLEAN DEFAULT FALSE,
    rating DECIMAL(3,2) DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
    view_count BIGINT DEFAULT 0,
    traffic_estimate BIGINT DEFAULT 0,
    featured BOOLEAN DEFAULT FALSE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- 索引优化
    CONSTRAINT unique_slug UNIQUE (slug)
);

-- =====================================================
-- 4. 工具多语言信息表
-- =====================================================
CREATE TABLE tool_translations (
    id BIGSERIAL PRIMARY KEY,
    tool_id BIGINT NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL, -- 'en', 'cn', 'zh-CN' 等
    name VARCHAR(200) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    long_description TEXT,
    use_cases TEXT,
    target_audience TEXT,
    subcategory VARCHAR(100),

    UNIQUE(tool_id, language_code)
);

-- =====================================================
-- 5. 功能特性表
-- =====================================================
CREATE TABLE tool_features (
    id BIGSERIAL PRIMARY KEY,
    tool_id BIGINT NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL,
    feature_text TEXT NOT NULL,
    sort_order INTEGER DEFAULT 0
);

-- =====================================================
-- 6. 标签表
-- =====================================================
CREATE TABLE tags (
    id BIGSERIAL PRIMARY KEY,
    tag_key VARCHAR(100) UNIQUE NOT NULL, -- 用于关联的唯一标识
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 7. 标签多语言表
-- =====================================================
CREATE TABLE tag_translations (
    id BIGSERIAL PRIMARY KEY,
    tag_id BIGINT NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL,
    tag_name VARCHAR(100) NOT NULL,

    UNIQUE(tag_id, language_code)
);

-- =====================================================
-- 8. 工具标签关联表
-- =====================================================
CREATE TABLE tool_tags (
    tool_id BIGINT NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    tag_id BIGINT NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    tag_type VARCHAR(20) NOT NULL CHECK (tag_type IN ('general', 'industry')),

    PRIMARY KEY (tool_id, tag_id)
);

-- =====================================================
-- 9. 定价计划表
-- =====================================================
CREATE TABLE pricing_plans (
    id BIGSERIAL PRIMARY KEY,
    tool_id BIGINT NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL,
    plan_name VARCHAR(100) NOT NULL,
    price VARCHAR(50), -- 存储如 "$49.99", "免费" 等
    is_free_plan BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0
);

-- =====================================================
-- 10. 定价计划功能表
-- =====================================================
CREATE TABLE pricing_plan_features (
    id BIGSERIAL PRIMARY KEY,
    pricing_plan_id BIGINT NOT NULL REFERENCES pricing_plans(id) ON DELETE CASCADE,
    feature_text TEXT NOT NULL,
    sort_order INTEGER DEFAULT 0
);

-- =====================================================
-- 11. 元数据表（存储爬虫和处理信息）
-- =====================================================
CREATE TABLE tool_metadata (
    tool_id BIGINT PRIMARY KEY REFERENCES tools(id) ON DELETE CASCADE,
    source_site VARCHAR(100),
    crawl_timestamp TIMESTAMP WITH TIME ZONE,
    processing_timestamp TIMESTAMP WITH TIME ZONE,
    model_input_data TEXT -- JSON格式存储原始输入数据
);

-- ===============================
-- 索引优化（针对查询性能）
-- ===============================

-- 主要查询索引
CREATE INDEX idx_tools_category ON tools(category_id);
CREATE INDEX idx_tools_pricing_type ON tools(pricing_type);
CREATE INDEX idx_categories_key ON categories(category_key);
CREATE INDEX idx_tools_featured ON tools(featured);
CREATE INDEX idx_tools_status ON tools(status);
CREATE INDEX idx_tools_rating ON tools(rating DESC);
CREATE INDEX idx_tools_view_count ON tools(view_count DESC);

-- 多语言查询索引
CREATE INDEX idx_tool_translations_lang ON tool_translations(language_code);
CREATE INDEX idx_tool_translations_tool_lang ON tool_translations(tool_id, language_code);

-- 分类查询索引
CREATE INDEX idx_category_translations_lang ON category_translations(language_code);
CREATE INDEX idx_category_translations_cat_lang ON category_translations(category_id, language_code);

-- 全文搜索索引（支持中英文搜索）
CREATE INDEX idx_tool_translations_name_gin ON tool_translations USING gin(to_tsvector('english', name));
CREATE INDEX idx_tool_translations_title_gin ON tool_translations USING gin(to_tsvector('english', title));
CREATE INDEX idx_tool_translations_desc_gin ON tool_translations USING gin(to_tsvector('english', description));

-- 标签查询索引
CREATE INDEX idx_tool_tags_tool ON tool_tags(tool_id);
CREATE INDEX idx_tool_tags_tag ON tool_tags(tag_id);
CREATE INDEX idx_tool_tags_type ON tool_tags(tag_type);

-- ===============================
-- 更新时间戳触发器
-- ===============================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tools_updated_at BEFORE UPDATE ON tools
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===============================
-- 示例数据插入
-- ===============================

-- 先插入分类
INSERT INTO categories (category_key, sort_order) VALUES ('productivity', 1);
INSERT INTO categories (category_key, sort_order) VALUES ('ai-tools', 2);
INSERT INTO categories (category_key, sort_order) VALUES ('design', 3);

-- 插入分类多语言信息
DO $$
DECLARE
    productivity_id BIGINT;
    ai_tools_id BIGINT;
    design_id BIGINT;
BEGIN
    SELECT id INTO productivity_id FROM categories WHERE category_key = 'productivity';
    SELECT id INTO ai_tools_id FROM categories WHERE category_key = 'ai-tools';
    SELECT id INTO design_id FROM categories WHERE category_key = 'design';

    -- 生产力工具分类
    INSERT INTO category_translations (category_id, language_code, category_name, category_description) VALUES
    (productivity_id, 'en', 'Productivity', 'Tools to enhance your productivity and efficiency'),
    (productivity_id, 'cn', '生产力工具', '提升工作效率和生产力的工具');

    -- AI工具分类
    INSERT INTO category_translations (category_id, language_code, category_name, category_description) VALUES
    (ai_tools_id, 'en', 'AI Tools', 'Artificial Intelligence powered applications'),
    (ai_tools_id, 'cn', 'AI工具', '人工智能驱动的应用程序');

    -- 设计工具分类
    INSERT INTO category_translations (category_id, language_code, category_name, category_description) VALUES
    (design_id, 'en', 'Design', 'Creative design and visual tools'),
    (design_id, 'cn', '设计工具', '创意设计和视觉工具');
END $$;

-- 插入工具主信息（使用分类ID）
INSERT INTO tools (slug, url, page_screenshot, category_id, pricing_type, trial_available, rating, view_count, traffic_estimate, featured, status)
VALUES ('bee', 'https://www.bee.computer', 'screenshots/toolify/Bee.jpg',
        (SELECT id FROM categories WHERE category_key = 'productivity'),
        'paid', false, 0, 0, 0, false, 'active');

-- 获取刚插入的工具ID
DO $$
DECLARE
    tool_record_id BIGINT;
BEGIN
    SELECT id INTO tool_record_id FROM tools WHERE slug = 'bee';

    -- 插入英文翻译
    INSERT INTO tool_translations (tool_id, language_code, name, title, description, long_description, use_cases, target_audience, subcategory)
    VALUES (
        tool_record_id,
        'en',
        'BEE',
        'Bee - Your new wearable personal AI',
        'Bee is an AI-powered wearable device that understands and records your conversations, tasks, and locations in real-time, transforming them into summaries, personal insights, and timely reminders for enhanced productivity and information management.',
        'Bee is an innovative wearable AI assistant featuring advanced noise filtering technology and up to 7 days of battery life. Supporting 40 languages, it can be accessed through a standalone device, iOS app, or Apple Watch. The device quietly learns user behavior patterns, preferences, and relationships over time, building a comprehensive understanding of the user''s world. It features a modular design for various wearing options and includes a clip for easy attachment to clothing.',
        'Meeting documentation, daily conversation summaries, personal task management, life logging and review, multilingual communication assistance',
        'Business professionals, knowledge workers, students, multilingual users, individuals seeking smart life assistance',
        'Wearable AI Assistant'
    );

    -- 插入中文翻译
    INSERT INTO tool_translations (tool_id, language_code, name, title, description, long_description, use_cases, target_audience, subcategory)
    VALUES (
        tool_record_id,
        'cn',
        'BEE',
        'Bee - 你的可穿戴个人AI助手',
        'Bee是一款AI驱动的可穿戴设备，能够实时理解和记录你的对话、任务和位置信息，将其转化为摘要、个人洞察和及时提醒，帮助提升个人生产力和信息管理效率。',
        'Bee是一款创新的可穿戴AI助手，具备先进的噪声过滤技术和长达7天的电池续航。设备支持40种语言理解，可通过独立设备、iOS应用或Apple Watch进行交互。它能够在后台安静地学习用户的行为模式、偏好和人际关系，随时间推移建立对用户世界的深入理解。设备采用模块化设计，可以多种方式佩戴，并配备便携夹子方便固定在衣物上。',
        '会议记录、日常对话总结、个人任务管理、生活记录与回顾、多语言交流辅助',
        '商务人士、知识工作者、学生、多语言使用者、需要智能生活助手的个人用户',
        '可穿戴AI助手'
    );
END $$;

-- ===============================
-- 常用查询视图
-- ===============================

-- 创建工具详情视图（包含多语言信息和分类）
CREATE VIEW v_tool_details AS
SELECT
    t.id,
    t.slug,
    t.url,
    t.page_screenshot,
    c.category_key,
    ct.category_name,
    ct.category_description,
    t.pricing_type,
    t.trial_available,
    t.rating,
    t.view_count,
    t.traffic_estimate,
    t.featured,
    t.status,
    t.created_at,
    t.updated_at,
    tt.language_code,
    tt.name,
    tt.title,
    tt.description,
    tt.long_description,
    tt.use_cases,
    tt.target_audience,
    tt.subcategory
FROM tools t
LEFT JOIN categories c ON t.category_id = c.id
LEFT JOIN category_translations ct ON c.id = ct.category_id
LEFT JOIN tool_translations tt ON t.id = tt.tool_id AND tt.language_code = ct.language_code
WHERE t.status = 'active';

-- 创建工具搜索视图
CREATE VIEW v_tool_search AS
SELECT
    t.id,
    t.slug,
    c.category_key,
    ct.category_name,
    t.pricing_type,
    t.rating,
    t.featured,
    tt.language_code,
    tt.name,
    tt.title,
    tt.description,
    -- 创建搜索向量（包含分类名称）
    to_tsvector('english', COALESCE(tt.name, '') || ' ' || COALESCE(tt.title, '') || ' ' || COALESCE(tt.description, '') || ' ' || COALESCE(ct.category_name, '')) as search_vector
FROM tools t
LEFT JOIN categories c ON t.category_id = c.id
LEFT JOIN category_translations ct ON c.id = ct.category_id
LEFT JOIN tool_translations tt ON t.id = tt.tool_id AND tt.language_code = ct.language_code
WHERE t.status = 'active';

-- 创建分类列表视图
CREATE VIEW v_categories AS
SELECT
    c.id,
    c.category_key,
    c.sort_order,
    ct.language_code,
    ct.category_name,
    ct.category_description,
    COUNT(t.id) as tool_count
FROM categories c
LEFT JOIN category_translations ct ON c.id = ct.category_id
LEFT JOIN tools t ON c.id = t.category_id AND t.status = 'active'
GROUP BY c.id, c.category_key, c.sort_order, ct.language_code, ct.category_name, ct.category_description
ORDER BY c.sort_order;
