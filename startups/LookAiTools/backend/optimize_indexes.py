#!/usr/bin/env python3
"""
数据库索引优化脚本
立即提升查询性能
"""

import asyncio
import asyncpg
import os
from dotenv import load_dotenv

async def optimize_database_indexes():
    """执行数据库索引优化"""
    load_dotenv()
    database_url = os.getenv('DATABASE_URL')
    
    if not database_url:
        print("❌ 错误: DATABASE_URL 环境变量未设置")
        return
    
    try:
        conn = await asyncpg.connect(database_url)
        print("🚀 开始执行数据库索引优化...")
        
        # 关键索引列表
        indexes = [
            {
                'name': 'tools状态索引',
                'sql': 'CREATE INDEX IF NOT EXISTS idx_tools_status ON tools(status);'
            },
            {
                'name': 'tools精选复合索引',
                'sql': 'CREATE INDEX IF NOT EXISTS idx_tools_featured_status_view ON tools(status, featured DESC, view_count DESC, created_at DESC);'
            },
            {
                'name': 'tools分类状态索引',
                'sql': 'CREATE INDEX IF NOT EXISTS idx_tools_category_status ON tools(category_id, status);'
            },
            {
                'name': 'tools浏览量索引(活跃)',
                'sql': "CREATE INDEX IF NOT EXISTS idx_tools_view_count_desc ON tools(view_count DESC) WHERE status = 'active';"
            },
            {
                'name': 'tools创建时间索引(活跃)',
                'sql': "CREATE INDEX IF NOT EXISTS idx_tools_created_desc ON tools(created_at DESC) WHERE status = 'active';"
            },
            {
                'name': 'tool_translations复合索引',
                'sql': 'CREATE INDEX IF NOT EXISTS idx_tool_translations_tool_lang ON tool_translations(tool_id, language_code);'
            },
            {
                'name': 'categories键索引',
                'sql': 'CREATE INDEX IF NOT EXISTS idx_categories_key ON categories(category_key);'
            },
            {
                'name': 'category_translations复合索引',
                'sql': 'CREATE INDEX IF NOT EXISTS idx_category_translations_cat_lang ON category_translations(category_id, language_code);'
            }
        ]
        
        # 执行索引创建
        success_count = 0
        for i, index in enumerate(indexes):
            try:
                print(f"📝 [{i+1}/{len(indexes)}] 创建 {index['name']}...")
                await conn.execute(index['sql'])
                print("   ✅ 成功")
                success_count += 1
            except Exception as e:
                print(f"   ⚠️  警告: {e}")
        
        # 更新表统计信息
        print("\n📊 更新表统计信息...")
        tables = ['tools', 'tool_translations', 'categories', 'category_translations']
        for table in tables:
            try:
                await conn.execute(f'ANALYZE {table};')
                print(f"   ✅ {table} 统计信息已更新")
            except Exception as e:
                print(f"   ⚠️  {table} 更新失败: {e}")
        
        # 显示创建的索引
        print("\n📋 查看已创建的索引:")
        indexes_result = await conn.fetch("""
            SELECT 
                tablename,
                indexname
            FROM pg_indexes 
            WHERE tablename IN ('tools', 'tool_translations', 'categories', 'category_translations')
            AND indexname LIKE 'idx_%'
            ORDER BY tablename, indexname;
        """)
        
        current_table = None
        for idx in indexes_result:
            if idx['tablename'] != current_table:
                current_table = idx['tablename']
                print(f"\n  📋 {current_table}:")
            print(f"    - {idx['indexname']}")
        
        await conn.close()
        print(f"\n🎉 索引优化完成! 成功创建 {success_count}/{len(indexes)} 个索引")
        print("💡 建议: 重启应用服务器以获得最佳性能")
        
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")

if __name__ == "__main__":
    asyncio.run(optimize_database_indexes())
