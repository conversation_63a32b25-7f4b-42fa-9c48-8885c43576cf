-- 数据库初始化脚本 - 示例数据
-- 插入基础分类数据、分类翻译数据等

-- ===============================
-- 插入更多分类数据
-- ===============================

-- 先插入更多分类
INSERT INTO categories (category_key, sort_order) VALUES 
('productivity', 1),
('ai-tools', 2),
('design', 3),
('chatbot', 4),
('image', 5),
('video', 6),
('code-it', 7),
('business', 8),
('marketing', 9),
('text-writing', 10),
('3d', 11),
('voice', 12),
('education', 13),
('life-assistant', 14),
('ai-tools-directory', 15),
('prompt', 16),
('ai-detector', 17),
('other', 18)
ON CONFLICT (category_key) DO NOTHING;

-- ===============================
-- 插入分类翻译数据
-- ===============================

DO $
DECLARE
    cat_record RECORD;
BEGIN
    -- 为每个分类插入英文和中文翻译
    FOR cat_record IN SELECT id, category_key FROM categories LOOP
        -- 插入英文翻译
        INSERT INTO category_translations (category_id, language_code, category_name, category_description)
        VALUES (
            cat_record.id, 
            'en',
            CASE cat_record.category_key
                WHEN 'productivity' THEN 'Productivity'
                WHEN 'ai-tools' THEN 'AI Tools'
                WHEN 'design' THEN 'Design'
                WHEN 'chatbot' THEN 'Chatbot'
                WHEN 'image' THEN 'Image'
                WHEN 'video' THEN 'Video'
                WHEN 'code-it' THEN 'Code & IT'
                WHEN 'business' THEN 'Business'
                WHEN 'marketing' THEN 'Marketing'
                WHEN 'text-writing' THEN 'Text & Writing'
                WHEN '3d' THEN '3D'
                WHEN 'voice' THEN 'Voice'
                WHEN 'education' THEN 'Education'
                WHEN 'life-assistant' THEN 'Life Assistant'
                WHEN 'ai-tools-directory' THEN 'AI Tools Directory'
                WHEN 'prompt' THEN 'Prompt'
                WHEN 'ai-detector' THEN 'AI Detector'
                ELSE 'Other'
            END,
            CASE cat_record.category_key
                WHEN 'productivity' THEN 'Tools to enhance your productivity and efficiency'
                WHEN 'ai-tools' THEN 'Artificial Intelligence powered applications'
                WHEN 'design' THEN 'Creative design and visual tools'
                WHEN 'chatbot' THEN 'AI chatbot and conversation tools'
                WHEN 'image' THEN 'AI image generation and editing tools'
                WHEN 'video' THEN 'AI video generation and editing tools'
                WHEN 'code-it' THEN 'AI programming and development tools'
                WHEN 'business' THEN 'AI business and enterprise tools'
                WHEN 'marketing' THEN 'AI marketing and promotion tools'
                WHEN 'text-writing' THEN 'AI writing and content creation tools'
                WHEN '3d' THEN 'AI 3D modeling and design tools'
                WHEN 'voice' THEN 'AI voice and audio tools'
                WHEN 'education' THEN 'AI education and learning tools'
                WHEN 'life-assistant' THEN 'AI life and personal assistant tools'
                WHEN 'ai-tools-directory' THEN 'AI tools directory and discovery platforms'
                WHEN 'prompt' THEN 'AI prompt engineering tools'
                WHEN 'ai-detector' THEN 'AI detection and verification tools'
                ELSE 'Other AI tools and utilities'
            END
        ) ON CONFLICT (category_id, language_code) DO NOTHING;

        -- 插入中文翻译
        INSERT INTO category_translations (category_id, language_code, category_name, category_description)
        VALUES (
            cat_record.id, 
            'cn',
            CASE cat_record.category_key
                WHEN 'productivity' THEN '生产力工具'
                WHEN 'ai-tools' THEN 'AI工具'
                WHEN 'design' THEN '设计工具'
                WHEN 'chatbot' THEN '聊天机器人'
                WHEN 'image' THEN '图像工具'
                WHEN 'video' THEN '视频工具'
                WHEN 'code-it' THEN '编程IT'
                WHEN 'business' THEN '商业工具'
                WHEN 'marketing' THEN '营销工具'
                WHEN 'text-writing' THEN '文本写作'
                WHEN '3d' THEN '3D工具'
                WHEN 'voice' THEN '语音工具'
                WHEN 'education' THEN '教育工具'
                WHEN 'life-assistant' THEN '生活助手'
                WHEN 'ai-tools-directory' THEN 'AI工具目录'
                WHEN 'prompt' THEN '提示词'
                WHEN 'ai-detector' THEN 'AI检测'
                ELSE '其他'
            END,
            CASE cat_record.category_key
                WHEN 'productivity' THEN '提升工作效率和生产力的工具'
                WHEN 'ai-tools' THEN '人工智能驱动的应用程序'
                WHEN 'design' THEN '创意设计和视觉工具'
                WHEN 'chatbot' THEN 'AI聊天机器人和对话工具'
                WHEN 'image' THEN 'AI图像生成和编辑工具'
                WHEN 'video' THEN 'AI视频生成和编辑工具'
                WHEN 'code-it' THEN 'AI编程和开发工具'
                WHEN 'business' THEN 'AI商业和企业工具'
                WHEN 'marketing' THEN 'AI营销和推广工具'
                WHEN 'text-writing' THEN 'AI写作和内容创作工具'
                WHEN '3d' THEN 'AI 3D建模和设计工具'
                WHEN 'voice' THEN 'AI语音和音频工具'
                WHEN 'education' THEN 'AI教育和学习工具'
                WHEN 'life-assistant' THEN 'AI生活和个人助手工具'
                WHEN 'ai-tools-directory' THEN 'AI工具目录和发现平台'
                WHEN 'prompt' THEN 'AI提示词工程工具'
                WHEN 'ai-detector' THEN 'AI检测和验证工具'
                ELSE '其他AI工具和实用程序'
            END
        ) ON CONFLICT (category_id, language_code) DO NOTHING;
    END LOOP;
END $;

-- ===============================
-- 插入示例标签数据
-- ===============================

-- 插入标签主数据
INSERT INTO tags (tag_key) VALUES 
('chatbot'),
('writing'),
('image-generation'),
('code-assistant'),
('free'),
('api'),
('no-code'),
('real-time'),
('collaboration'),
('enterprise'),
('automation'),
('machine-learning'),
('natural-language'),
('computer-vision'),
('data-analysis')
ON CONFLICT (tag_key) DO NOTHING;

-- 插入标签翻译数据
DO $
DECLARE
    tag_record RECORD;
BEGIN
    FOR tag_record IN SELECT id, tag_key FROM tags LOOP
        -- 插入英文翻译
        INSERT INTO tag_translations (tag_id, language_code, tag_name)
        VALUES (
            tag_record.id, 
            'en',
            CASE tag_record.tag_key
                WHEN 'chatbot' THEN 'Chatbot'
                WHEN 'writing' THEN 'Writing'
                WHEN 'image-generation' THEN 'Image Generation'
                WHEN 'code-assistant' THEN 'Code Assistant'
                WHEN 'free' THEN 'Free'
                WHEN 'api' THEN 'API'
                WHEN 'no-code' THEN 'No Code'
                WHEN 'real-time' THEN 'Real-time'
                WHEN 'collaboration' THEN 'Collaboration'
                WHEN 'enterprise' THEN 'Enterprise'
                WHEN 'automation' THEN 'Automation'
                WHEN 'machine-learning' THEN 'Machine Learning'
                WHEN 'natural-language' THEN 'Natural Language'
                WHEN 'computer-vision' THEN 'Computer Vision'
                WHEN 'data-analysis' THEN 'Data Analysis'
                ELSE REPLACE(tag_record.tag_key, '-', ' ')
            END
        ) ON CONFLICT (tag_id, language_code) DO NOTHING;

        -- 插入中文翻译
        INSERT INTO tag_translations (tag_id, language_code, tag_name)
        VALUES (
            tag_record.id, 
            'cn',
            CASE tag_record.tag_key
                WHEN 'chatbot' THEN '聊天机器人'
                WHEN 'writing' THEN '写作'
                WHEN 'image-generation' THEN '图像生成'
                WHEN 'code-assistant' THEN '编程助手'
                WHEN 'free' THEN '免费'
                WHEN 'api' THEN 'API接口'
                WHEN 'no-code' THEN '无代码'
                WHEN 'real-time' THEN '实时'
                WHEN 'collaboration' THEN '协作'
                WHEN 'enterprise' THEN '企业级'
                WHEN 'automation' THEN '自动化'
                WHEN 'machine-learning' THEN '机器学习'
                WHEN 'natural-language' THEN '自然语言'
                WHEN 'computer-vision' THEN '计算机视觉'
                WHEN 'data-analysis' THEN '数据分析'
                ELSE tag_record.tag_key
            END
        ) ON CONFLICT (tag_id, language_code) DO NOTHING;
    END LOOP;
END $;

-- ===============================
-- 插入示例工具数据
-- ===============================

-- 插入示例工具 - ChatGPT
INSERT INTO tools (slug, url, page_screenshot, category_id, pricing_type, trial_available, rating, view_count, traffic_estimate, featured, status)
VALUES ('chatgpt', 'https://chat.openai.com', 'screenshots/chatgpt.jpg', 
        (SELECT id FROM categories WHERE category_key = 'chatbot'), 
        'freemium', true, 4.8, 1000000, 500000000, true, 'active')
ON CONFLICT (slug) DO NOTHING;

-- 插入ChatGPT翻译信息
DO $$
DECLARE
    chatgpt_id BIGINT;
BEGIN
    SELECT id INTO chatgpt_id FROM tools WHERE slug = 'chatgpt';
    
    IF chatgpt_id IS NOT NULL THEN
        -- 插入英文翻译
        INSERT INTO tool_translations (tool_id, language_code, name, title, description, long_description, use_cases, target_audience, subcategory)
        VALUES (
            chatgpt_id, 
            'en', 
            'ChatGPT', 
            'ChatGPT - AI Conversational Assistant',
            'ChatGPT is an AI-powered conversational assistant that can help with writing, analysis, coding, and creative tasks through natural language interactions.',
            'ChatGPT is a state-of-the-art AI language model developed by OpenAI that can engage in human-like conversations and assist with a wide variety of tasks including writing, coding, analysis, creative projects, and problem-solving. It uses advanced natural language processing to understand context and provide helpful, accurate responses.',
            'Content writing, code assistance, research, creative brainstorming, problem solving, language translation, educational support',
            'Students, professionals, developers, writers, researchers, businesses',
            'Conversational AI'
        ) ON CONFLICT (tool_id, language_code) DO NOTHING;
        
        -- 插入中文翻译
        INSERT INTO tool_translations (tool_id, language_code, name, title, description, long_description, use_cases, target_audience, subcategory)
        VALUES (
            chatgpt_id, 
            'cn', 
            'ChatGPT', 
            'ChatGPT - AI对话助手',
            'ChatGPT是一个AI驱动的对话助手，可以通过自然语言交互帮助用户进行写作、分析、编程和创意任务。',
            'ChatGPT是由OpenAI开发的最先进的AI语言模型，能够进行类人对话并协助完成各种任务，包括写作、编程、分析、创意项目和问题解决。它使用先进的自然语言处理技术来理解上下文并提供有用、准确的回应。',
            '内容写作、代码辅助、研究、创意头脑风暴、问题解决、语言翻译、教育支持',
            '学生、专业人士、开发者、作家、研究人员、企业',
            '对话式AI'
        ) ON CONFLICT (tool_id, language_code) DO NOTHING;
    END IF;
END $$;

COMMIT;

-- 显示插入结果
SELECT 'Categories inserted:' as info, COUNT(*) as count FROM categories
UNION ALL
SELECT 'Category translations inserted:', COUNT(*) FROM category_translations
UNION ALL
SELECT 'Tags inserted:', COUNT(*) FROM tags
UNION ALL
SELECT 'Tag translations inserted:', COUNT(*) FROM tag_translations
UNION ALL
SELECT 'Tools inserted:', COUNT(*) FROM tools
UNION ALL
SELECT 'Tool translations inserted:', COUNT(*) FROM tool_translations;
