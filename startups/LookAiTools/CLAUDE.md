# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

LookAiTools is a comprehensive AI tool discovery platform that helps users find and evaluate AI tools. The platform crawls data from various sources, processes it with LLM services, and provides a multilingual interface for browsing and searching AI tools.

## Development Commands

### Backend (Python FastAPI)
```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run development server
uvicorn main:app --reload --port 8000

# Database operations
alembic upgrade head
```

### Frontend (React + Vite)
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Lint code
npm run lint

# Preview production build
npm run preview
```

### Crawler
```bash
# Navigate to crawler directory
cd crawler

# Install dependencies
pip install -r requirements.txt

# Run crawler
python run_crawler.py
```

## Project Architecture

### High-Level Structure
- **Backend**: FastAPI application with PostgreSQL database
- **Frontend**: React/Vite application with TypeScript and Tailwind CSS
- **Crawler**: Python-based web crawler for collecting AI tool data
- **Database**: PostgreSQL with multilingual support

### Key Directories
- `backend/app/`: FastAPI application code
- `frontend/src/`: React application source code
- `crawler/`: Web scraping and data collection scripts
- `docs/`: Project documentation and architecture guides

### Database Schema
The application uses a PostgreSQL database with these key tables:
- `ai_tools`: Core tool information
- `categories`: Tool categories
- `tool_translations`: Multilingual content
- `users`: User management
- `user_behaviors`: Analytics and tracking

### API Architecture
- RESTful API with FastAPI
- Multilingual support with language parameter
- Pagination for large datasets
- Performance monitoring middleware
- Database connection pooling

### Frontend Architecture
- React 18 with TypeScript
- Vite for build tooling
- Tailwind CSS for styling
- i18next for internationalization
- Zustand for state management
- React Router for navigation

## Data Processing Pipeline

### LLM Integration
The platform uses AWS Bedrock Claude for:
- Content standardization and translation
- Tool description enhancement
- Category classification
- Data quality improvement

### Crawler System
- Toolify.ai data collection
- Automated data standardization
- Screenshot capture
- Metadata extraction

## Environment Setup

### Required Environment Variables
```bash
# Database
DATABASE_URL=postgresql://username:password@host:port/database

# AWS Bedrock (for LLM services)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-west-2
```

### Database Connection
The project uses Neon PostgreSQL cloud database. Connection details are in the README.md.

## Performance Considerations

### Backend Optimizations
- Connection pooling with asyncpg
- GZip compression middleware
- Query optimization with indexes
- Slow query monitoring
- Batch data processing

### Frontend Optimizations
- Client-side pagination
- Lazy loading for images
- Component memoization
- Bundle optimization with Vite

## Testing

### Backend Testing
```bash
cd backend
python -m pytest
```

### Frontend Testing
```bash
cd frontend
npm test
```

### Performance Testing
Run the performance test scripts in the root directory:
```bash
./test_performance.sh
```

## Deployment Notes

### Development Environment
```bash
# Start all services
docker-compose up -d
```

### Production Deployment
- Backend: Deploy FastAPI with uvicorn
- Frontend: Build and serve static files
- Database: Use managed PostgreSQL service
- Monitoring: Performance and error tracking

## Code Standards

### Backend (Python)
- Use FastAPI conventions
- Async/await for database operations
- Proper error handling with HTTPException
- Type hints with Pydantic models
- Database queries with asyncpg

### Frontend (TypeScript)
- React functional components with hooks
- TypeScript for type safety
- Tailwind CSS for styling
- Consistent component structure
- Proper error boundaries

### Common Patterns
- Multilingual content handling
- API response formatting
- Database query optimization
- Error handling and logging
- Performance monitoring

## Debugging

### Backend Debugging
- Check uvicorn logs for API errors
- Monitor database connection pool
- Review slow query logs
- Verify environment variables

### Frontend Debugging
- Use browser developer tools
- Check network requests in DevTools
- Review console errors
- Verify API responses

### Database Debugging
- Use PostgreSQL logs
- Check query performance
- Verify connection pooling
- Monitor memory usage